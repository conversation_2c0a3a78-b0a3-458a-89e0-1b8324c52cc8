# 🎬 StreamFlix Android Apps

Native Android applications for mobile and TV platforms, built with modern Android development practices.

## 📱 Apps Overview

### Mobile App (`/mobile`)
- **Target**: Android phones and tablets
- **UI Framework**: Material Design 3
- **Navigation**: Bottom navigation with fragments
- **Features**: Touch-optimized interface, portrait/landscape support

### TV App (`/tv`)
- **Target**: Android TV and Google TV
- **UI Framework**: Leanback library
- **Navigation**: D-pad navigation with focus management
- **Features**: 10-foot UI, remote control support

## 🏗️ Architecture

### Tech Stack
- **Language**: Kotlin
- **Architecture**: MVVM with Repository pattern
- **Dependency Injection**: Hilt (Dagger)
- **Network**: Retrofit + OkHttp
- **Database**: Room
- **Image Loading**: Glide
- **Video Player**: ExoPlayer
- **Async**: Coroutines + Flow

### Project Structure
```
android_apps/
├── mobile/                 # Mobile app
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── java/com/streamflix/mobile/
│   │   │   │   ├── ui/          # UI components
│   │   │   │   ├── data/        # Data layer
│   │   │   │   ├── domain/      # Domain layer
│   │   │   │   └── utils/       # Utilities
│   │   │   ├── res/             # Resources
│   │   │   └── AndroidManifest.xml
│   │   └── build.gradle
│   └── gradle/
├── tv/                     # TV app
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── java/com/streamflix/tv/
│   │   │   │   ├── ui/          # TV UI components
│   │   │   │   ├── data/        # Data layer
│   │   │   │   ├── domain/      # Domain layer
│   │   │   │   └── utils/       # TV utilities
│   │   │   ├── res/             # TV resources
│   │   │   └── AndroidManifest.xml
│   │   └── build.gradle
│   └── gradle/
├── build_all.bat           # Build script
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK (API 21+)
- JDK 8 or later
- Git

### Setup Instructions

1. **Clone the repository**
```bash
git clone <repository-url>
cd android_apps
```

2. **Open in Android Studio**
- Open Android Studio
- Select "Open an existing project"
- Navigate to `android_apps/mobile` or `android_apps/tv`
- Let Gradle sync

3. **Configure API endpoint**
```kotlin
// In build.gradle (app level)
buildConfigField "String", "API_BASE_URL", '"http://your-domain.com/api"'
```

4. **Build and run**
```bash
# Using Android Studio: Click Run button
# Using command line: ./gradlew assembleDebug
```

## 🔨 Building

### Using Build Script (Recommended)
```bash
# Run the automated build script
build_all.bat

# Options:
# 1. Build Mobile App (Debug)
# 2. Build Mobile App (Release)
# 3. Build TV App (Debug)
# 4. Build TV App (Release)
# 5. Build All Apps
# 6. Install Mobile App
# 7. Install TV App
# 8. Clean All Projects
```

### Manual Building
```bash
# Mobile App
cd mobile
./gradlew assembleDebug      # Debug build
./gradlew assembleRelease    # Release build

# TV App
cd tv
./gradlew assembleDebug      # Debug build
./gradlew assembleRelease    # Release build
```

### Build Outputs
```
android_apps/
├── streamflix-mobile-debug.apk
├── streamflix-mobile-release.apk
├── streamflix-tv-debug.apk
└── streamflix-tv-release.apk
```

## 📱 Installation

### Mobile App
```bash
# Install debug version
adb install streamflix-mobile-debug.apk

# Install release version
adb install streamflix-mobile-release.apk
```

### TV App
```bash
# Install debug version
adb install streamflix-tv-debug.apk

# Install release version
adb install streamflix-tv-release.apk
```

### Sideloading on Android TV
1. Enable Developer Options on your Android TV
2. Enable USB Debugging
3. Connect via ADB: `adb connect <TV_IP_ADDRESS>`
4. Install APK: `adb install streamflix-tv-release.apk`

## 🎯 Features

### Mobile App Features
- ✅ **Home Screen**: Featured content carousel
- ✅ **Browse**: Movies and TV shows with filters
- ✅ **Search**: Advanced search with suggestions
- ✅ **Player**: Full-screen video player with controls
- ✅ **Profile**: User account management
- ✅ **Watchlist**: Personal content collection
- ✅ **Continue Watching**: Resume playback
- ✅ **Downloads**: Offline content (planned)
- ✅ **Picture-in-Picture**: Background playback

### TV App Features
- ✅ **Leanback UI**: 10-foot interface design
- ✅ **D-pad Navigation**: Remote control support
- ✅ **Focus Management**: Visual focus indicators
- ✅ **Background Updates**: Dynamic backgrounds
- ✅ **Recommendations**: Content suggestions
- ✅ **Voice Search**: Search integration (planned)
- ✅ **Channels**: TV provider integration (planned)

### Shared Features
- ✅ **Authentication**: Login/register/guest mode
- ✅ **Content Streaming**: Multiple server support
- ✅ **Quality Selection**: 480p, 720p, 1080p, Auto
- ✅ **Subtitle Support**: Multiple languages
- ✅ **Progress Tracking**: Resume from last position
- ✅ **Offline Storage**: Local data caching
- ✅ **Error Handling**: Graceful error recovery

## 🎮 TV Remote Controls

### Navigation
- **D-Pad**: Navigate between UI elements
- **Select/Enter**: Activate focused element
- **Back**: Go back or exit
- **Home**: Return to home screen
- **Menu**: Show context menu

### Player Controls
- **Play/Pause**: Toggle playback
- **Fast Forward**: Seek forward
- **Rewind**: Seek backward
- **Volume Up/Down**: Adjust volume
- **Mute**: Toggle mute

## 🔧 Configuration

### API Configuration
```kotlin
// build.gradle (app level)
android {
    buildTypes {
        debug {
            buildConfigField "String", "API_BASE_URL", '"http://********:8000/api"'
        }
        release {
            buildConfigField "String", "API_BASE_URL", '"https://your-domain.com/api"'
        }
    }
}
```

### Network Security
```xml
<!-- res/xml/network_security_config.xml -->
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">your-domain.com</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
</network-security-config>
```

## 🧪 Testing

### Unit Tests
```bash
./gradlew test
```

### Instrumentation Tests
```bash
./gradlew connectedAndroidTest
```

### UI Tests
```bash
./gradlew connectedDebugAndroidTest
```

## 📦 Dependencies

### Core Dependencies
```gradle
// UI & Navigation
implementation 'androidx.appcompat:appcompat:1.6.1'
implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
implementation 'com.google.android.material:material:1.11.0'

// Architecture
implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
implementation 'androidx.room:room-ktx:2.6.1'

// Dependency Injection
implementation 'com.google.dagger:hilt-android:2.48.1'

// Network
implementation 'com.squareup.retrofit2:retrofit:2.9.0'
implementation 'com.squareup.okhttp3:okhttp:4.12.0'

// Media
implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
implementation 'com.github.bumptech.glide:glide:4.16.0'
```

### TV-Specific Dependencies
```gradle
// Leanback for TV
implementation 'androidx.leanback:leanback:1.0.0'
implementation 'androidx.tvprovider:tvprovider:1.0.0'
implementation 'androidx.recommendation:recommendation:1.0.0'
```

## 🐛 Troubleshooting

### Common Issues

**Build Errors**
```bash
# Clean and rebuild
./gradlew clean
./gradlew build
```

**Network Issues**
- Check API endpoint configuration
- Verify network security config
- Test with emulator vs real device

**TV Navigation Issues**
- Ensure focusable attributes are set
- Check focus order in layouts
- Verify remote key handling

**Video Playback Issues**
- Check ExoPlayer configuration
- Verify video URL format
- Test with different video sources

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on both mobile and TV
5. Submit a pull request

---

**Built with ❤️ for StreamFlix Android Platform**
