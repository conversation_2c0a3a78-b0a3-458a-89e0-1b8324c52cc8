package com.streamflix.tv.ui.main

import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.FragmentActivity
import androidx.leanback.app.BackgroundManager
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HeaderItem
import androidx.leanback.widget.ListRow
import androidx.leanback.widget.ListRowPresenter
import androidx.leanback.widget.ObjectAdapter
import androidx.leanback.widget.OnItemViewClickedListener
import androidx.leanback.widget.OnItemViewSelectedListener
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.Row
import androidx.leanback.widget.RowPresenter
import com.streamflix.tv.R
import com.streamflix.tv.databinding.ActivityMainBinding
import com.streamflix.tv.ui.presenter.ContentPresenter
import com.streamflix.tv.ui.presenter.HeroPresenter
import com.streamflix.tv.domain.model.Content
import com.streamflix.tv.utils.BackgroundHelper
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : FragmentActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()

    private lateinit var backgroundManager: BackgroundManager
    private lateinit var backgroundHelper: BackgroundHelper
    private lateinit var rowsAdapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupBackground()
        setupBrowseFragment()
        setupObservers()

        // Load data
        viewModel.loadHomeData()
    }

    private fun setupBackground() {
        backgroundManager = BackgroundManager.getInstance(this)
        backgroundManager.attach(window)
        backgroundHelper = BackgroundHelper(this, backgroundManager)
    }

    private fun setupBrowseFragment() {
        val browseFragment = binding.browseFragment

        // Set up the browse fragment
        browseFragment.apply {
            title = getString(R.string.app_name)
            headersState = androidx.leanback.app.BrowseSupportFragment.HEADERS_ENABLED
            isHeadersTransitionOnBackEnabled = true
            brandColor = resources.getColor(R.color.primary_color, null)
            searchAffordanceColor = resources.getColor(R.color.primary_color, null)
        }

        // Set up adapters
        rowsAdapter = ArrayObjectAdapter(ListRowPresenter())
        browseFragment.adapter = rowsAdapter

        // Set up listeners
        browseFragment.onItemViewClickedListener = ItemViewClickedListener()
        browseFragment.onItemViewSelectedListener = ItemViewSelectedListener()
    }

    private fun setupObservers() {
        viewModel.heroContent.observe(this) { content ->
            if (content.isNotEmpty()) {
                addHeroRow(content)
            }
        }

        viewModel.featuredMovies.observe(this) { movies ->
            if (movies.isNotEmpty()) {
                addContentRow("Featured Movies", movies)
            }
        }

        viewModel.trendingMovies.observe(this) { movies ->
            if (movies.isNotEmpty()) {
                addContentRow("Trending Movies", movies)
            }
        }

        viewModel.featuredTvShows.observe(this) { tvShows ->
            if (tvShows.isNotEmpty()) {
                addContentRow("Featured TV Shows", tvShows)
            }
        }

        viewModel.trendingTvShows.observe(this) { tvShows ->
            if (tvShows.isNotEmpty()) {
                addContentRow("Trending TV Shows", tvShows)
            }
        }

        viewModel.continueWatching.observe(this) { content ->
            if (content.isNotEmpty()) {
                addContentRow("Continue Watching", content)
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            // Handle loading state
        }

        viewModel.error.observe(this) { error ->
            // Handle error state
        }
    }

    private fun addHeroRow(content: List<Content>) {
        val heroAdapter = ArrayObjectAdapter(HeroPresenter())
        content.forEach { heroAdapter.add(it) }

        val headerItem = HeaderItem(0, "Featured")
        val listRow = ListRow(headerItem, heroAdapter)
        rowsAdapter.add(listRow)
    }

    private fun addContentRow(title: String, content: List<Content>) {
        val contentAdapter = ArrayObjectAdapter(ContentPresenter())
        content.forEach { contentAdapter.add(it) }

        val headerItem = HeaderItem(rowsAdapter.size().toLong(), title)
        val listRow = ListRow(headerItem, contentAdapter)
        rowsAdapter.add(listRow)
    }

    private inner class ItemViewClickedListener : OnItemViewClickedListener {
        override fun onItemClicked(
            itemViewHolder: Presenter.ViewHolder?,
            item: Any?,
            rowViewHolder: RowPresenter.ViewHolder?,
            row: Row?
        ) {
            if (item is Content) {
                // Navigate to details or player
                viewModel.onContentClicked(item)
            }
        }
    }

    private inner class ItemViewSelectedListener : OnItemViewSelectedListener {
        override fun onItemSelected(
            itemViewHolder: Presenter.ViewHolder?,
            item: Any?,
            rowViewHolder: RowPresenter.ViewHolder?,
            row: Row?
        ) {
            if (item is Content) {
                // Update background
                backgroundHelper.updateBackground(item.backdropPath)
            }
        }
    }
}
