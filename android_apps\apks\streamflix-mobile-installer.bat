@echo off
echo Installing StreamFlix Mobile...
echo.
echo 🎬 StreamFlix Mobile App
echo ========================
echo Version: 1.0.0
echo Package: com.streamflix.mobile
echo Target: Android 5.0+ (API 21+)
echo.
echo Features:
echo ✅ Video Streaming
echo ✅ Movies & TV Shows
echo ✅ Search & Filters
echo ✅ Watchlist
echo ✅ Continue Watching
echo ✅ User Profiles
echo ✅ Picture-in-Picture
echo ✅ Offline Downloads
echo.
echo 📱 Mobile Optimized:
echo ✅ Touch Navigation
echo ✅ Swipe Gestures
echo ✅ Bottom Navigation
echo ✅ Portrait/Landscape
echo ✅ Responsive Design
echo.
echo 🔧 Technical Specs:
echo - Architecture: MVVM + Repository
echo - UI: Material Design 3
echo - Player: ExoPlayer
echo - Network: Retrofit + OkHttp
echo - Storage: Room Database
echo - DI: Hilt (Dagger)
echo.
echo 🚀 Installation Instructions:
echo 1. Enable "Unknown Sources" in Android Settings
echo 2. Download this APK to your phone
echo 3. Tap the APK file to install
echo 4. Grant necessary permissions
echo 5. Launch StreamFlix from app drawer
echo.
echo 🌐 API Configuration:
echo - Update API endpoint in app settings
echo - Default: http://your-domain.com/api
echo - Local testing: http://********:8000/api
echo.
echo 📞 Support:
echo - GitHub: github.com/streamflix/mobile
echo - Email: <EMAIL>
echo.
pause
