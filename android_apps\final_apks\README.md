# 🎬 StreamFlix Android Apps - Final Build

## 📦 Package Contents

- `streamflix-mobile.apk` - Mobile app for phones/tablets
- `streamflix-tv.apk` - TV app for Android TV/Google TV
- `install-mobile.bat` - Mobile installation guide
- `install-tv.bat` - TV installation guide

## 📱 Mobile App Installation

1. Enable "Unknown Sources" in Android Settings
2. Transfer `streamflix-mobile.apk` to your Android device
3. Tap the APK file to install
4. Launch StreamFlix from app drawer

## 📺 TV App Installation

### Method 1: ADB Sideloading
```bash
adb connect [TV_IP_ADDRESS]
adb install streamflix-tv.apk
```

### Method 2: File Manager
1. Copy APK to USB drive
2. Connect USB to Android TV
3. Use file manager to install

## 🔧 Configuration

1. Open app settings
2. Navigate to "Server Settings"
3. Enter your API URL:
   - Production: `https://your-domain.com/api`
   - Local: `http://*************:8000/api`

## 🎯 Features

### Mobile App
- Touch-optimized interface
- Video streaming with quality selection
- Search and content discovery
- Watchlist management
- Continue watching
- Picture-in-picture mode

### TV App
- Leanback interface for TV
- D-pad navigation
- Remote control support
- Content recommendations
- 4K video support

## 📞 Support

For issues or questions:
- Check app settings for API configuration
- Verify network connectivity
- Ensure StreamFlix server is running

Built with ❤️ for StreamFlix
