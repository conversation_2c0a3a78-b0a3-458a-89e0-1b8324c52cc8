plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.streamflix.tv'
    compileSdk 34

    defaultConfig {
        applicationId "com.streamflix.tv"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // API Configuration
        buildConfigField "String", "API_BASE_URL", '"http://your-domain.com/api"'
        buildConfigField "String", "TMDB_IMAGE_BASE_URL", '"https://image.tmdb.org/t/p/"'
        buildConfigField "boolean", "DEBUG_MODE", "true"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"http://10.0.2.2:8000/api"'
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"https://your-domain.com/api"'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'

    // Leanback for TV
    implementation 'androidx.leanback:leanback:1.0.0'
    implementation 'androidx.leanback:leanback-preference:1.0.0'
    implementation 'androidx.tvprovider:tvprovider:1.0.0'

    // Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Dependency Injection
    implementation 'com.google.dagger:hilt-android:2.48.1'
    kapt 'com.google.dagger:hilt-compiler:2.48.1'

    // Network
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Image Loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'jp.wasabeef:glide-transformations:4.3.0'

    // Video Player
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-cast:2.19.1'

    // Local Storage
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'

    // Preferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    implementation 'androidx.preference:preference-ktx:1.2.1'

    // Material Design
    implementation 'com.google.android.material:material:1.11.0'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // ViewPager2
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // Work Manager
    implementation 'androidx.work:work-runtime-ktx:2.9.0'

    // Splash Screen
    implementation 'androidx.core:core-splashscreen:1.0.1'

    // Lottie Animations
    implementation 'com.airbnb.android:lottie:6.2.0'

    // Shimmer Effect
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Media Session
    implementation 'androidx.media:media:1.7.0'
    implementation 'androidx.media2:media2-session:1.3.0'
    implementation 'androidx.media2:media2-widget:1.3.0'

    // TV Input Framework
    implementation 'androidx.tvprovider:tvprovider:1.0.0'

    // Recommendation Engine
    implementation 'androidx.recommendation:recommendation:1.0.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
