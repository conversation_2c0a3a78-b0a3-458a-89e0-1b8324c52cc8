<?php
/**
 * Database Structure Checker
 * Automatically checks and suggests database updates
 */

class DatabaseChecker {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }
    
    /**
     * Check if database needs updates
     */
    public function needsUpdate() {
        $checks = [
            $this->checkTable('embed_servers'),
            $this->checkTable('site_settings'),
            $this->checkTable('user_activity'),
            $this->checkTable('watchlist'),
            $this->checkColumn('movies', 'is_featured'),
            $this->checkColumn('movies', 'is_trending'),
            $this->checkColumn('tv_shows', 'is_featured'),
            $this->checkColumn('tv_shows', 'is_trending')
        ];
        
        return in_array(false, $checks);
    }
    
    /**
     * Get missing components
     */
    public function getMissingComponents() {
        $missing = [];
        
        // Check tables
        $required_tables = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
        foreach ($required_tables as $table) {
            if (!$this->checkTable($table)) {
                $missing['tables'][] = $table;
            }
        }
        
        // Check columns
        $required_columns = [
            'movies' => ['is_featured', 'is_trending', 'created_at'],
            'tv_shows' => ['is_featured', 'is_trending', 'created_at']
        ];
        
        foreach ($required_columns as $table => $columns) {
            foreach ($columns as $column) {
                if (!$this->checkColumn($table, $column)) {
                    $missing['columns'][$table][] = $column;
                }
            }
        }
        
        return $missing;
    }
    
    /**
     * Check if table exists
     */
    private function checkTable($table_name) {
        try {
            $stmt = $this->conn->query("SHOW TABLES LIKE '{$table_name}'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if column exists in table
     */
    private function checkColumn($table_name, $column_name) {
        try {
            $stmt = $this->conn->query("SHOW COLUMNS FROM {$table_name} LIKE '{$column_name}'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get database version/status
     */
    public function getDatabaseStatus() {
        $status = [
            'version' => '1.0.0',
            'needs_update' => $this->needsUpdate(),
            'missing' => $this->getMissingComponents(),
            'tables_count' => 0,
            'total_records' => 0
        ];
        
        try {
            // Count tables
            $stmt = $this->conn->query("SHOW TABLES");
            $status['tables_count'] = $stmt->rowCount();
            
            // Count total records
            $tables = ['movies', 'tv_shows', 'users', 'genres'];
            foreach ($tables as $table) {
                try {
                    $stmt = $this->conn->query("SELECT COUNT(*) FROM {$table}");
                    $status['total_records'] += $stmt->fetchColumn();
                } catch (Exception $e) {
                    // Table might not exist
                }
            }
            
        } catch (Exception $e) {
            $status['error'] = $e->getMessage();
        }
        
        return $status;
    }
    
    /**
     * Auto-fix critical issues
     */
    public function autoFix() {
        $fixed = [];
        
        try {
            $this->conn->beginTransaction();
            
            // Create critical tables if missing
            if (!$this->checkTable('embed_servers')) {
                $this->conn->exec("
                    CREATE TABLE embed_servers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        movie_url TEXT NOT NULL,
                        tv_url TEXT NOT NULL,
                        priority INT DEFAULT 1,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                $fixed[] = 'Created embed_servers table';
            }
            
            // Add critical columns
            if (!$this->checkColumn('movies', 'is_featured')) {
                $this->conn->exec("ALTER TABLE movies ADD COLUMN is_featured BOOLEAN DEFAULT 0");
                $fixed[] = 'Added is_featured column to movies';
            }
            
            if (!$this->checkColumn('movies', 'is_trending')) {
                $this->conn->exec("ALTER TABLE movies ADD COLUMN is_trending BOOLEAN DEFAULT 0");
                $fixed[] = 'Added is_trending column to movies';
            }
            
            if (!$this->checkColumn('tv_shows', 'is_featured')) {
                $this->conn->exec("ALTER TABLE tv_shows ADD COLUMN is_featured BOOLEAN DEFAULT 0");
                $fixed[] = 'Added is_featured column to tv_shows';
            }
            
            if (!$this->checkColumn('tv_shows', 'is_trending')) {
                $this->conn->exec("ALTER TABLE tv_shows ADD COLUMN is_trending BOOLEAN DEFAULT 0");
                $fixed[] = 'Added is_trending column to tv_shows';
            }
            
            $this->conn->commit();
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
        
        return $fixed;
    }
}

/**
 * Quick database check function
 */
function checkDatabaseHealth() {
    try {
        $checker = new DatabaseChecker();
        return $checker->getDatabaseStatus();
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage(),
            'needs_update' => true
        ];
    }
}

/**
 * Display database update notification
 */
function showDatabaseUpdateNotification() {
    $status = checkDatabaseHealth();
    
    if ($status['needs_update'] && !empty($status['missing'])) {
        echo '<div class="database-update-notification" style="
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        ">';
        
        echo '<div>';
        echo '<strong>⚠️ Database Update Required</strong><br>';
        echo '<small>Your database needs updates to support new features.</small>';
        echo '</div>';
        
        echo '<div>';
        echo '<a href="database-updater.php" class="btn btn-warning" style="
            background: #ffc107;
            color: #000;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
        ">Update Now</a>';
        echo '</div>';
        
        echo '</div>';
    }
}
?>
