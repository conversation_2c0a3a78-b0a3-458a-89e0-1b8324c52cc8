@echo off
echo 🎬 StreamFlix Android Apps - Build Readiness Check
echo ===================================================

:: Set colors
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%📋 Checking build requirements...%NC%
echo.

:: Check Java
echo %BLUE%☕ Checking Java...%NC%
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✅ Java is installed%NC%
    java -version 2>&1 | findstr "version"
) else (
    echo %RED%❌ Java is not installed or not in PATH%NC%
    echo %YELLOW%Please install Java JDK 8 or later%NC%
    set "missing_deps=true"
)
echo.

:: Check Android SDK
echo %BLUE%📱 Checking Android SDK...%NC%
if defined ANDROID_HOME (
    echo %GREEN%✅ ANDROID_HOME is set: %ANDROID_HOME%%NC%
    if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
        echo %GREEN%✅ ADB is available%NC%
    ) else (
        echo %YELLOW%⚠️  ADB not found in platform-tools%NC%
    )
) else (
    echo %RED%❌ ANDROID_HOME is not set%NC%
    echo %YELLOW%Please install Android Studio and set ANDROID_HOME%NC%
    set "missing_deps=true"
)
echo.

:: Check project structure
echo %BLUE%📁 Checking project structure...%NC%

:: Check mobile app
if exist "mobile\app\build.gradle" (
    echo %GREEN%✅ Mobile app build.gradle found%NC%
) else (
    echo %RED%❌ Mobile app build.gradle missing%NC%
    set "missing_files=true"
)

if exist "mobile\app\src\main\AndroidManifest.xml" (
    echo %GREEN%✅ Mobile AndroidManifest.xml found%NC%
) else (
    echo %RED%❌ Mobile AndroidManifest.xml missing%NC%
    set "missing_files=true"
)

:: Check TV app
if exist "tv\app\build.gradle" (
    echo %GREEN%✅ TV app build.gradle found%NC%
) else (
    echo %RED%❌ TV app build.gradle missing%NC%
    set "missing_files=true"
)

if exist "tv\app\src\main\AndroidManifest.xml" (
    echo %GREEN%✅ TV AndroidManifest.xml found%NC%
) else (
    echo %RED%❌ TV AndroidManifest.xml missing%NC%
    set "missing_files=true"
)

:: Check Gradle wrapper
if exist "mobile\gradlew.bat" (
    echo %GREEN%✅ Mobile Gradle wrapper found%NC%
) else (
    echo %YELLOW%⚠️  Mobile Gradle wrapper missing%NC%
)

if exist "tv\gradlew.bat" (
    echo %GREEN%✅ TV Gradle wrapper found%NC%
) else (
    echo %YELLOW%⚠️  TV Gradle wrapper missing%NC%
)

echo.

:: Check build script
echo %BLUE%🔧 Checking build script...%NC%
if exist "build_all.bat" (
    echo %GREEN%✅ Build script found%NC%
) else (
    echo %RED%❌ Build script missing%NC%
    set "missing_files=true"
)
echo.

:: Summary
echo %BLUE%📊 Build Readiness Summary:%NC%
echo ========================

if not defined missing_deps if not defined missing_files (
    echo %GREEN%🎉 All requirements met! Ready to build.%NC%
    echo.
    echo %BLUE%🚀 To build the apps, run:%NC%
    echo   build_all.bat
    echo.
    echo %BLUE%📱 Build options available:%NC%
    echo   1. Mobile App (Debug/Release)
    echo   2. TV App (Debug/Release)
    echo   3. All Apps
    echo   4. Install Apps
) else (
    echo %RED%❌ Some requirements are missing:%NC%
    
    if defined missing_deps (
        echo %YELLOW%  - Install missing dependencies%NC%
    )
    
    if defined missing_files (
        echo %YELLOW%  - Some project files are missing%NC%
    )
    
    echo.
    echo %BLUE%💡 Setup instructions:%NC%
    echo   1. Install Android Studio
    echo   2. Set ANDROID_HOME environment variable
    echo   3. Install Java JDK 8+
    echo   4. Run this check again
)

echo.
echo %BLUE%📋 Current project structure:%NC%
echo android_apps/
echo ├── mobile/           # Mobile app
echo ├── tv/               # TV app
echo ├── build_all.bat     # Build script
echo └── README.md         # Documentation

echo.
pause
