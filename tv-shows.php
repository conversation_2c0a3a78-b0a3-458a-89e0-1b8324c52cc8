<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

$genre_filter = isset($_GET['genre']) ? (int)$_GET['genre'] : 0;
$sort = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'popularity';

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Build query
    $where_clause = "WHERE 1=1";
    $params = [];
    
    if ($genre_filter > 0) {
        $where_clause .= " AND t.id IN (SELECT tv_show_id FROM tv_show_genres WHERE genre_id = :genre_id)";
        $params[':genre_id'] = $genre_filter;
    }
    
    $order_clause = "ORDER BY ";
    switch ($sort) {
        case 'title':
            $order_clause .= "t.name ASC";
            break;
        case 'year':
            $order_clause .= "t.first_air_date DESC";
            break;
        case 'rating':
            $order_clause .= "t.vote_average DESC";
            break;
        default:
            $order_clause .= "t.popularity DESC";
    }
    
    // Get TV shows
    $stmt = $conn->prepare("
        SELECT t.*, COUNT(*) OVER() as total_count
        FROM tv_shows t 
        {$where_clause} 
        {$order_clause} 
        LIMIT :limit OFFSET :offset
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_count = $tv_shows[0]['total_count'] ?? 0;
    $total_pages = ceil($total_count / $limit);
    
    // Get genres for filter
    $stmt = $conn->query("SELECT * FROM genres ORDER BY name");
    $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $tv_shows = [];
    $genres = [];
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TV Shows - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .page-header {
            margin: 100px 0 30px;
            text-align: center;
        }
        
        .filters {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-group select {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 40px 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: var(--primary-color);
        }
        
        .pagination .current {
            background: var(--primary-color);
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .no-results h3 {
            color: var(--text-primary);
            margin-bottom: 10px;
        }
        
        .show-info {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
            
            <ul class="nav-links">
                <li><a href="index.php">Home</a></li>
                <li><a href="movies.php">Movies</a></li>
                <li><a href="tv-shows.php" class="active">TV Shows</a></li>
                <li><a href="genres.php">Genres</a></li>
                <?php if (isLoggedIn()): ?>
                    <li><a href="watchlist.php">My List</a></li>
                <?php endif; ?>
            </ul>
            
            <div class="user-menu">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search TV shows...">
                    <button class="search-btn">🔍</button>
                    <div class="search-results"></div>
                </div>
                
                <?php if (isLoggedIn()): ?>
                    <div class="user-dropdown">
                        <button class="user-btn"><?php echo $_SESSION['username']; ?></button>
                        <div class="dropdown-menu">
                            <a href="profile.php">Profile</a>
                            <a href="watchlist.php">My List</a>
                            <?php if (isAdmin()): ?>
                                <a href="admin/">Admin Panel</a>
                            <?php endif; ?>
                            <a href="logout.php">Logout</a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary">Sign In</a>
                <?php endif; ?>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="page-header">
                <h1>TV Shows</h1>
                <p>Discover amazing TV series to binge-watch</p>
            </div>
            
            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>Genre:</label>
                    <select onchange="filterShows()" id="genreFilter">
                        <option value="0">All Genres</option>
                        <?php foreach ($genres as $genre): ?>
                            <option value="<?php echo $genre['id']; ?>" <?php echo $genre_filter == $genre['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($genre['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>Sort by:</label>
                    <select onchange="filterShows()" id="sortFilter">
                        <option value="popularity" <?php echo $sort == 'popularity' ? 'selected' : ''; ?>>Popularity</option>
                        <option value="title" <?php echo $sort == 'title' ? 'selected' : ''; ?>>Title</option>
                        <option value="year" <?php echo $sort == 'year' ? 'selected' : ''; ?>>First Air Date</option>
                        <option value="rating" <?php echo $sort == 'rating' ? 'selected' : ''; ?>>Rating</option>
                    </select>
                </div>
            </div>
            
            <!-- TV Shows Grid -->
            <?php if (!empty($tv_shows)): ?>
                <div class="content-grid">
                    <?php foreach ($tv_shows as $show): ?>
                        <div class="content-card" data-id="<?php echo $show['id']; ?>" data-type="tv_show">
                            <img src="<?php echo getImageUrl($show['poster_path'], 'w500'); ?>" 
                                 alt="<?php echo htmlspecialchars($show['name']); ?>" 
                                 loading="lazy">
                            <div class="card-overlay">
                                <div class="card-title"><?php echo htmlspecialchars($show['name']); ?></div>
                                <div class="card-info">
                                    <span><?php echo date('Y', strtotime($show['first_air_date'])); ?></span>
                                    <div class="rating">
                                        <span class="star">★</span>
                                        <span><?php echo $show['vote_average']; ?></span>
                                    </div>
                                </div>
                                <div class="show-info">
                                    <?php if ($show['number_of_seasons']): ?>
                                        <?php echo $show['number_of_seasons']; ?> Season<?php echo $show['number_of_seasons'] > 1 ? 's' : ''; ?>
                                    <?php endif; ?>
                                    <?php if ($show['number_of_episodes']): ?>
                                        • <?php echo $show['number_of_episodes']; ?> Episodes
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&genre=<?php echo $genre_filter; ?>&sort=<?php echo $sort; ?>">← Previous</a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="?page=<?php echo $i; ?>&genre=<?php echo $genre_filter; ?>&sort=<?php echo $sort; ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&genre=<?php echo $genre_filter; ?>&sort=<?php echo $sort; ?>">Next →</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="no-results">
                    <h3>No TV shows found</h3>
                    <p>Try adjusting your filters or check back later for new content.</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Content Modal -->
    <div id="contentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-body">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        function filterShows() {
            const genre = document.getElementById('genreFilter').value;
            const sort = document.getElementById('sortFilter').value;
            
            const url = new URL(window.location);
            url.searchParams.set('genre', genre);
            url.searchParams.set('sort', sort);
            url.searchParams.set('page', '1');
            
            window.location.href = url.toString();
        }
    </script>
</body>
</html>
