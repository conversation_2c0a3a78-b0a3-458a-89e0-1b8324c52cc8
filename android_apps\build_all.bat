@echo off
echo 🎬 StreamFlix Android Apps Build Script
echo ========================================

:: Set colors
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: Check if Android SDK is available
echo %BLUE%📱 Checking Android SDK...%NC%
if not defined ANDROID_HOME (
    echo %RED%❌ ANDROID_HOME not set%NC%
    echo %YELLOW%Please install Android Studio and set ANDROID_HOME%NC%
    pause
    exit /b 1
)

echo %GREEN%✅ Android SDK found at %ANDROID_HOME%%NC%

:build_menu
echo.
echo %BLUE%🏗️  Build Options:%NC%
echo 1. Build Mobile App (Debug)
echo 2. Build Mobile App (Release)
echo 3. Build TV App (Debug)
echo 4. Build TV App (Release)
echo 5. Build All Apps
echo 6. Install Mobile App
echo 7. Install TV App
echo 8. Clean All Projects
echo 9. Exit

set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" goto build_mobile_debug
if "%choice%"=="2" goto build_mobile_release
if "%choice%"=="3" goto build_tv_debug
if "%choice%"=="4" goto build_tv_release
if "%choice%"=="5" goto build_all
if "%choice%"=="6" goto install_mobile
if "%choice%"=="7" goto install_tv
if "%choice%"=="8" goto clean_all
if "%choice%"=="9" goto end
goto build_menu

:build_mobile_debug
echo %BLUE%📱 Building Mobile App (Debug)...%NC%
cd mobile
call gradlew assembleDebug
if %errorlevel% equ 0 (
    echo %GREEN%✅ Mobile Debug APK built successfully%NC%
    copy "app\build\outputs\apk\debug\app-debug.apk" "..\streamflix-mobile-debug.apk"
) else (
    echo %RED%❌ Mobile Debug build failed%NC%
)
cd ..
goto build_menu

:build_mobile_release
echo %BLUE%📱 Building Mobile App (Release)...%NC%
cd mobile
call gradlew assembleRelease
if %errorlevel% equ 0 (
    echo %GREEN%✅ Mobile Release APK built successfully%NC%
    copy "app\build\outputs\apk\release\app-release.apk" "..\streamflix-mobile-release.apk"
) else (
    echo %RED%❌ Mobile Release build failed%NC%
)
cd ..
goto build_menu

:build_tv_debug
echo %BLUE%📺 Building TV App (Debug)...%NC%
cd tv
call gradlew assembleDebug
if %errorlevel% equ 0 (
    echo %GREEN%✅ TV Debug APK built successfully%NC%
    copy "app\build\outputs\apk\debug\app-debug.apk" "..\streamflix-tv-debug.apk"
) else (
    echo %RED%❌ TV Debug build failed%NC%
)
cd ..
goto build_menu

:build_tv_release
echo %BLUE%📺 Building TV App (Release)...%NC%
cd tv
call gradlew assembleRelease
if %errorlevel% equ 0 (
    echo %GREEN%✅ TV Release APK built successfully%NC%
    copy "app\build\outputs\apk\release\app-release.apk" "..\streamflix-tv-release.apk"
) else (
    echo %RED%❌ TV Release build failed%NC%
)
cd ..
goto build_menu

:build_all
echo %BLUE%🏗️  Building All Apps...%NC%

echo Building Mobile Debug...
cd mobile
call gradlew assembleDebug
if %errorlevel% equ 0 (
    copy "app\build\outputs\apk\debug\app-debug.apk" "..\streamflix-mobile-debug.apk"
    echo %GREEN%✅ Mobile Debug built%NC%
)
cd ..

echo Building Mobile Release...
cd mobile
call gradlew assembleRelease
if %errorlevel% equ 0 (
    copy "app\build\outputs\apk\release\app-release.apk" "..\streamflix-mobile-release.apk"
    echo %GREEN%✅ Mobile Release built%NC%
)
cd ..

echo Building TV Debug...
cd tv
call gradlew assembleDebug
if %errorlevel% equ 0 (
    copy "app\build\outputs\apk\debug\app-debug.apk" "..\streamflix-tv-debug.apk"
    echo %GREEN%✅ TV Debug built%NC%
)
cd ..

echo Building TV Release...
cd tv
call gradlew assembleRelease
if %errorlevel% equ 0 (
    copy "app\build\outputs\apk\release\app-release.apk" "..\streamflix-tv-release.apk"
    echo %GREEN%✅ TV Release built%NC%
)
cd ..

echo %GREEN%🎉 All apps built successfully!%NC%
goto build_menu

:install_mobile
echo %BLUE%📱 Installing Mobile App...%NC%
if exist "streamflix-mobile-debug.apk" (
    adb install -r streamflix-mobile-debug.apk
    if %errorlevel% equ 0 (
        echo %GREEN%✅ Mobile app installed successfully%NC%
    ) else (
        echo %RED%❌ Mobile app installation failed%NC%
    )
) else (
    echo %RED%❌ Mobile APK not found. Build it first.%NC%
)
goto build_menu

:install_tv
echo %BLUE%📺 Installing TV App...%NC%
if exist "streamflix-tv-debug.apk" (
    adb install -r streamflix-tv-debug.apk
    if %errorlevel% equ 0 (
        echo %GREEN%✅ TV app installed successfully%NC%
    ) else (
        echo %RED%❌ TV app installation failed%NC%
    )
) else (
    echo %RED%❌ TV APK not found. Build it first.%NC%
)
goto build_menu

:clean_all
echo %BLUE%🧹 Cleaning all projects...%NC%
cd mobile
call gradlew clean
cd ..\tv
call gradlew clean
cd ..
echo %GREEN%✅ All projects cleaned%NC%
goto build_menu

:end
echo.
echo %GREEN%🎬 Build Summary:%NC%
echo ================
if exist "streamflix-mobile-debug.apk" echo ✅ Mobile Debug APK: streamflix-mobile-debug.apk
if exist "streamflix-mobile-release.apk" echo ✅ Mobile Release APK: streamflix-mobile-release.apk
if exist "streamflix-tv-debug.apk" echo ✅ TV Debug APK: streamflix-tv-debug.apk
if exist "streamflix-tv-release.apk" echo ✅ TV Release APK: streamflix-tv-release.apk

echo.
echo %BLUE%📱 Installation Commands:%NC%
echo adb install streamflix-mobile-debug.apk
echo adb install streamflix-tv-debug.apk

echo.
echo %GREEN%🎉 Thank you for using StreamFlix!%NC%
pause
