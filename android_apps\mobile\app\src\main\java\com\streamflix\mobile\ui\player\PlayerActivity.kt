package com.streamflix.mobile.ui.player

import android.app.PictureInPictureParams
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Rational
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.PlaybackException
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.streamflix.mobile.databinding.ActivityPlayerBinding
import com.streamflix.mobile.domain.model.Content
import com.streamflix.mobile.utils.gone
import com.streamflix.mobile.utils.visible
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PlayerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPlayerBinding
    private val viewModel: PlayerViewModel by viewModels()

    private var exoPlayer: ExoPlayer? = null
    private var isFullscreen = false
    private var isInPictureInPictureMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Keep screen on
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        setupPlayer()
        setupObservers()
        setupControls()

        // Get content from intent
        val content = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("content", Content::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra("content")
        }

        content?.let {
            viewModel.loadContent(it)
        }
    }

    private fun setupPlayer() {
        exoPlayer = ExoPlayer.Builder(this).build().apply {
            binding.playerView.player = this
            
            addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_BUFFERING -> {
                            binding.progressBar.visible()
                        }
                        Player.STATE_READY -> {
                            binding.progressBar.gone()
                        }
                        Player.STATE_ENDED -> {
                            // Handle playback end
                            viewModel.onPlaybackEnded()
                        }
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    binding.progressBar.gone()
                    viewModel.onPlayerError(error.message ?: "Playback error")
                }
            })
        }

        // Configure player view
        binding.playerView.apply {
            setShowBuffering(com.google.android.exoplayer2.ui.PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            controllerAutoShow = true
            controllerHideOnTouch = true
            resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
        }
    }

    private fun setupObservers() {
        viewModel.videoUrl.observe(this) { url ->
            if (url.isNotEmpty()) {
                playVideo(url)
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            if (isLoading) {
                binding.progressBar.visible()
            } else {
                binding.progressBar.gone()
            }
        }

        viewModel.error.observe(this) { error ->
            if (error != null) {
                // Show error message
                binding.errorText.text = error
                binding.errorText.visible()
            } else {
                binding.errorText.gone()
            }
        }

        viewModel.availableServers.observe(this) { servers ->
            // Update server selection UI
        }
    }

    private fun setupControls() {
        binding.btnFullscreen.setOnClickListener {
            toggleFullscreen()
        }

        binding.btnPip.setOnClickListener {
            enterPictureInPictureMode()
        }

        binding.btnBack.setOnClickListener {
            onBackPressed()
        }

        binding.btnSettings.setOnClickListener {
            // Show settings dialog
            showSettingsDialog()
        }
    }

    private fun playVideo(url: String) {
        val mediaItem = MediaItem.fromUri(Uri.parse(url))
        exoPlayer?.apply {
            setMediaItem(mediaItem)
            prepare()
            playWhenReady = true
        }
    }

    private fun toggleFullscreen() {
        if (isFullscreen) {
            exitFullscreen()
        } else {
            enterFullscreen()
        }
    }

    private fun enterFullscreen() {
        isFullscreen = true
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, binding.root).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FILL
        binding.btnFullscreen.setImageResource(android.R.drawable.ic_menu_revert)
    }

    private fun exitFullscreen() {
        isFullscreen = false
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        
        WindowCompat.setDecorFitsSystemWindows(window, true)
        WindowInsetsControllerCompat(window, binding.root).show(WindowInsetsCompat.Type.systemBars())

        binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
        binding.btnFullscreen.setImageResource(android.R.drawable.ic_menu_crop)
    }

    private fun enterPictureInPictureMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val aspectRatio = Rational(16, 9)
            val params = PictureInPictureParams.Builder()
                .setAspectRatio(aspectRatio)
                .build()
            
            enterPictureInPictureMode(params)
        }
    }

    private fun showSettingsDialog() {
        // Implement settings dialog for quality, subtitles, etc.
    }

    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean,
        newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        this.isInPictureInPictureMode = isInPictureInPictureMode
        
        if (isInPictureInPictureMode) {
            // Hide controls in PiP mode
            binding.playerView.hideController()
        } else {
            // Show controls when exiting PiP mode
            binding.playerView.showController()
        }
    }

    override fun onStop() {
        super.onStop()
        if (!isInPictureInPictureMode) {
            exoPlayer?.pause()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
        exoPlayer = null
        
        // Clear keep screen on flag
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onBackPressed() {
        if (isFullscreen) {
            exitFullscreen()
        } else {
            super.onBackPressed()
        }
    }
}
