<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get analytics data
    $analytics = [];
    
    // Content statistics
    $stmt = $conn->query("
        SELECT 
            (SELECT COUNT(*) FROM movies) as total_movies,
            (SELECT COUNT(*) FROM tv_shows) as total_tv_shows,
            (SELECT COUNT(*) FROM users) as total_users,
            (SELECT COUNT(*) FROM embed_servers WHERE is_active = 1) as active_servers
    ");
    $analytics['overview'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Popular content
    $stmt = $conn->query("
        SELECT title, vote_average, popularity, release_date 
        FROM movies 
        ORDER BY popularity DESC 
        LIMIT 10
    ");
    $analytics['popular_movies'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("
        SELECT name as title, vote_average, popularity, first_air_date as release_date 
        FROM tv_shows 
        ORDER BY popularity DESC 
        LIMIT 10
    ");
    $analytics['popular_tv_shows'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent additions
    $stmt = $conn->query("
        SELECT title, created_at, 'movie' as type 
        FROM movies 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("
        SELECT name as title, created_at, 'tv_show' as type 
        FROM tv_shows 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $analytics['recent_content'] = array_merge($recent_movies, $recent_tv_shows);
    usort($analytics['recent_content'], function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    $analytics['recent_content'] = array_slice($analytics['recent_content'], 0, 15);
    
    // User statistics
    $stmt = $conn->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    $analytics['user_registrations'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Content by genre
    $stmt = $conn->query("
        SELECT g.name, COUNT(mg.movie_id) as movie_count, COUNT(tg.tv_show_id) as tv_count
        FROM genres g
        LEFT JOIN movie_genres mg ON g.id = mg.genre_id
        LEFT JOIN tv_show_genres tg ON g.id = tg.genre_id
        GROUP BY g.id, g.name
        HAVING (movie_count > 0 OR tv_count > 0)
        ORDER BY (movie_count + tv_count) DESC
        LIMIT 10
    ");
    $analytics['content_by_genre'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $analytics = [];
    $error = 'Failed to load analytics: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .analytics-card {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
        }
        
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: var(--dark-bg);
            border-radius: 6px;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .content-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .content-item:last-child {
            border-bottom: none;
        }
        
        .content-title {
            color: var(--text-primary);
            font-weight: 500;
            flex: 1;
        }
        
        .content-meta {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .genre-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .genre-name {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .genre-count {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .chart-container {
            height: 200px;
            display: flex;
            align-items: end;
            gap: 5px;
            padding: 10px 0;
        }
        
        .chart-bar {
            background: var(--primary-color);
            border-radius: 2px 2px 0 0;
            min-height: 5px;
            flex: 1;
            position: relative;
        }
        
        .chart-bar:hover::after {
            content: attr(data-value);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-bg);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }
            
            .overview-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Analytics Dashboard</h1>
            <p>Monitor your platform's performance and content statistics</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php" class="active">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="analytics-grid">
            <!-- Overview -->
            <div class="analytics-card">
                <h3 class="card-title">Platform Overview</h3>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_movies'] ?? 0); ?></div>
                        <div class="stat-label">Movies</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_tv_shows'] ?? 0); ?></div>
                        <div class="stat-label">TV Shows</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['total_users'] ?? 0); ?></div>
                        <div class="stat-label">Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo number_format($analytics['overview']['active_servers'] ?? 0); ?></div>
                        <div class="stat-label">Active Servers</div>
                    </div>
                </div>
            </div>

            <!-- Popular Movies -->
            <div class="analytics-card">
                <h3 class="card-title">Popular Movies</h3>
                <div class="content-list">
                    <?php foreach ($analytics['popular_movies'] ?? [] as $movie): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="content-meta">★ <?php echo $movie['vote_average']; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Popular TV Shows -->
            <div class="analytics-card">
                <h3 class="card-title">Popular TV Shows</h3>
                <div class="content-list">
                    <?php foreach ($analytics['popular_tv_shows'] ?? [] as $show): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($show['title']); ?></div>
                            <div class="content-meta">★ <?php echo $show['vote_average']; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Recent Content -->
            <div class="analytics-card">
                <h3 class="card-title">Recent Additions</h3>
                <div class="content-list">
                    <?php foreach ($analytics['recent_content'] ?? [] as $content): ?>
                        <div class="content-item">
                            <div class="content-title"><?php echo htmlspecialchars($content['title']); ?></div>
                            <div class="content-meta">
                                <?php echo ucfirst(str_replace('_', ' ', $content['type'])); ?> • 
                                <?php echo date('M j', strtotime($content['created_at'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Content by Genre -->
            <div class="analytics-card">
                <h3 class="card-title">Content by Genre</h3>
                <div class="content-list">
                    <?php foreach ($analytics['content_by_genre'] ?? [] as $genre): ?>
                        <div class="genre-stats">
                            <div class="genre-name"><?php echo htmlspecialchars($genre['name']); ?></div>
                            <div class="genre-count">
                                <?php echo ($genre['movie_count'] + $genre['tv_count']); ?> items
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- User Registrations Chart -->
            <div class="analytics-card">
                <h3 class="card-title">User Registrations (Last 30 Days)</h3>
                <div class="chart-container">
                    <?php 
                    $max_registrations = 0;
                    foreach ($analytics['user_registrations'] ?? [] as $day) {
                        $max_registrations = max($max_registrations, $day['count']);
                    }
                    
                    foreach (array_reverse($analytics['user_registrations'] ?? []) as $day): 
                        $height = $max_registrations > 0 ? ($day['count'] / $max_registrations) * 100 : 0;
                    ?>
                        <div class="chart-bar" 
                             style="height: <?php echo $height; ?>%"
                             data-value="<?php echo $day['count']; ?> users on <?php echo date('M j', strtotime($day['date'])); ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
