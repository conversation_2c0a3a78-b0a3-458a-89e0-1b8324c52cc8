<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

$tmdb_id = isset($_GET['tmdb_id']) ? (int)$_GET['tmdb_id'] : 0;

if (!$tmdb_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid TMDB ID']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get TV show ID from TMDB ID
    $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE tmdb_id = :tmdb_id");
    $stmt->execute([':tmdb_id' => $tmdb_id]);
    $tv_show = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tv_show) {
        echo json_encode(['success' => false, 'message' => 'TV show not found']);
        exit();
    }
    
    // Get seasons
    $stmt = $conn->prepare("
        SELECT * FROM seasons 
        WHERE tv_show_id = :tv_show_id 
        ORDER BY season_number
    ");
    $stmt->execute([':tv_show_id' => $tv_show['id']]);
    $seasons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'seasons' => $seasons
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
