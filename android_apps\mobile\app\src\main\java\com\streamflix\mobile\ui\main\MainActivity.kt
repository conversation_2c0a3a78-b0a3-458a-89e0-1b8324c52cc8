package com.streamflix.mobile.ui.main

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.streamflix.mobile.R
import com.streamflix.mobile.databinding.ActivityMainBinding
import com.streamflix.mobile.ui.auth.AuthActivity
import com.streamflix.mobile.ui.home.HomeFragment
import com.streamflix.mobile.ui.movies.MoviesFragment
import com.streamflix.mobile.ui.profile.ProfileFragment
import com.streamflix.mobile.ui.search.SearchFragment
import com.streamflix.mobile.ui.tvshows.TvShowsFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()

    private val fragments = mapOf(
        R.id.nav_home to HomeFragment(),
        R.id.nav_movies to MoviesFragment(),
        R.id.nav_tv_shows to TvShowsFragment(),
        R.id.nav_search to SearchFragment(),
        R.id.nav_profile to ProfileFragment()
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        val splashScreen = installSplashScreen()
        
        super.onCreate(savedInstanceState)
        
        // Keep splash screen visible while checking auth
        splashScreen.setKeepOnScreenCondition { viewModel.isLoading.value }
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupObservers()
        setupBottomNavigation()
        
        // Check authentication status
        viewModel.checkAuthStatus()
    }

    private fun setupObservers() {
        viewModel.isAuthenticated.observe(this) { isAuthenticated ->
            if (isAuthenticated == false) {
                // User not authenticated, go to auth screen
                startActivity(Intent(this, AuthActivity::class.java))
                finish()
            }
        }

        viewModel.isLoading.observe(this) { isLoading ->
            // Splash screen will automatically hide when isLoading becomes false
        }
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            val fragment = fragments[item.itemId]
            if (fragment != null) {
                replaceFragment(fragment)
                true
            } else {
                false
            }
        }

        // Set default fragment
        binding.bottomNavigation.selectedItemId = R.id.nav_home
    }

    private fun replaceFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()
    }

    override fun onBackPressed() {
        val currentFragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
        
        // If not on home fragment, go to home
        if (currentFragment !is HomeFragment) {
            binding.bottomNavigation.selectedItemId = R.id.nav_home
        } else {
            // On home fragment, exit app
            super.onBackPressed()
        }
    }
}
