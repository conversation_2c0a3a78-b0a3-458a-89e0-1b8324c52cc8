<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    $activities = [];
    
    // Get recent movies
    $stmt = $conn->prepare("
        SELECT 'Movie Added' as type, title, created_at 
        FROM movies 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get recent TV shows
    $stmt = $conn->prepare("
        SELECT 'TV Show Added' as type, name as title, created_at 
        FROM tv_shows 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get recent users
    $stmt = $conn->prepare("
        SELECT 'User Registered' as type, username as title, created_at 
        FROM users 
        WHERE role = 'user'
        ORDER BY created_at DESC 
        LIMIT 3
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Combine all activities
    $all_activities = array_merge($movies, $tv_shows, $users);
    
    // Sort by date
    usort($all_activities, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    // Format activities
    $activities = array_slice($all_activities, 0, 10);
    foreach ($activities as &$activity) {
        $activity['date'] = date('M j, Y H:i', strtotime($activity['created_at']));
        unset($activity['created_at']);
    }
    
    echo json_encode([
        'success' => true,
        'activities' => $activities
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
