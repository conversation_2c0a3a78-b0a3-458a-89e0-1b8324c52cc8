<?php
// Database Configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'streamflix';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        return $this->conn;
    }
}

// TMDB API Configuration
define('TMDB_API_KEY', '6a22df4196745281fa0beba769ad867f');
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE', 'https://image.tmdb.org/t/p/');

// Embed Servers Configuration
$embed_servers = [
    'autoembed' => [
        'name' => 'AutoEmbed',
        'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
        'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
        'priority' => 1
    ],
    'vidjoy' => [
        'name' => 'VidJoy',
        'movie_url' => 'https://vidjoy.pro/embed/movie/{id}',
        'tv_url' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
        'priority' => 2
    ],
    'vidzee' => [
        'name' => 'VidZee',
        'movie_url' => 'https://player.vidzee.wtf/embed/movie/{id}',
        'tv_url' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
        'priority' => 3
    ],
    'letsembed' => [
        'name' => 'LetsEmbed',
        'movie_url' => 'https://letsembed.cc/embed/movie/?id={id}',
        'tv_url' => 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}',
        'priority' => 4
    ]
];

// Site Configuration
define('SITE_NAME', 'StreamFlix');
define('SITE_URL', 'http://localhost/streamflix');
define('ADMIN_EMAIL', '<EMAIL>');

// Security
define('JWT_SECRET', '414b8ffa10b4dff76ea382b189534b07e1a2d32ebff922ac2034d09eb89148dd');
define('ENCRYPTION_KEY', '516c18c1f64044b07207b3c664fba04db9af64b96c4deb914a0e5f74a60ffef2');

session_start();
?>