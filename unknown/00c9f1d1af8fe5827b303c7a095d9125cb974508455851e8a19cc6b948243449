<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirectTo('login.php');
}

try {
    $db = new Database();
    $conn = $db->connect();
    $user_id = $_SESSION['user_id'];
    
    // Get user's watchlist
    $stmt = $conn->prepare("
        SELECT 
            w.*,
            CASE 
                WHEN w.content_type = 'movie' THEN m.title
                ELSE t.name
            END as title,
            CASE 
                WHEN w.content_type = 'movie' THEN m.poster_path
                ELSE t.poster_path
            END as poster_path,
            CASE 
                WHEN w.content_type = 'movie' THEN m.release_date
                ELSE t.first_air_date
            END as release_date,
            CASE 
                WHEN w.content_type = 'movie' THEN m.vote_average
                ELSE t.vote_average
            END as vote_average,
            CASE 
                WHEN w.content_type = 'movie' THEN m.overview
                ELSE t.overview
            END as overview
        FROM watchlist w
        LEFT JOIN movies m ON w.content_type = 'movie' AND w.content_id = m.id
        LEFT JOIN tv_shows t ON w.content_type = 'tv_show' AND w.content_id = t.id
        WHERE w.user_id = :user_id
        ORDER BY w.added_at DESC
    ");
    $stmt->execute([':user_id' => $user_id]);
    $watchlist = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $watchlist = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Watchlist - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .page-header {
            margin: 100px 0 30px;
            text-align: center;
        }
        
        .watchlist-stats {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 4px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .empty-watchlist {
            text-align: center;
            padding: 60px 20px;
            background: var(--secondary-color);
            border-radius: 8px;
        }
        
        .empty-watchlist h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
        }
        
        .empty-watchlist p {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
        
        .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .content-card:hover .remove-btn {
            display: flex;
        }
        
        .remove-btn:hover {
            background: var(--primary-color);
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }
        
        .filter-tab {
            padding: 10px 20px;
            background: var(--secondary-color);
            color: var(--text-primary);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active,
        .filter-tab:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
            
            <ul class="nav-links">
                <li><a href="index.php">Home</a></li>
                <li><a href="movies.php">Movies</a></li>
                <li><a href="tv-shows.php">TV Shows</a></li>
                <li><a href="genres.php">Genres</a></li>
                <li><a href="watchlist.php" class="active">My List</a></li>
            </ul>
            
            <div class="user-menu">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search watchlist...">
                    <button class="search-btn">🔍</button>
                    <div class="search-results"></div>
                </div>
                
                <div class="user-dropdown">
                    <button class="user-btn"><?php echo $_SESSION['username']; ?></button>
                    <div class="dropdown-menu">
                        <a href="profile.php">Profile</a>
                        <a href="watchlist.php">My List</a>
                        <?php if (isAdmin()): ?>
                            <a href="admin/">Admin Panel</a>
                        <?php endif; ?>
                        <a href="logout.php">Logout</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="page-header">
                <h1>My Watchlist</h1>
                <p>Your saved movies and TV shows</p>
            </div>
            
            <?php if (!empty($watchlist)): ?>
                <!-- Stats -->
                <div class="watchlist-stats">
                    <h3>Watchlist Statistics</h3>
                    <div class="stats-grid">
                        <?php
                        $total_items = count($watchlist);
                        $movies_count = count(array_filter($watchlist, function($item) { return $item['content_type'] === 'movie'; }));
                        $tv_shows_count = $total_items - $movies_count;
                        ?>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $total_items; ?></div>
                            <div>Total Items</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $movies_count; ?></div>
                            <div>Movies</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $tv_shows_count; ?></div>
                            <div>TV Shows</div>
                        </div>
                    </div>
                </div>
                
                <!-- Filter Tabs -->
                <div class="filter-tabs">
                    <button class="filter-tab active" onclick="filterWatchlist('all')">All</button>
                    <button class="filter-tab" onclick="filterWatchlist('movie')">Movies</button>
                    <button class="filter-tab" onclick="filterWatchlist('tv_show')">TV Shows</button>
                </div>
                
                <!-- Watchlist Grid -->
                <div class="content-grid" id="watchlistGrid">
                    <?php foreach ($watchlist as $item): ?>
                        <div class="content-card" data-id="<?php echo $item['content_id']; ?>" 
                             data-type="<?php echo $item['content_type']; ?>"
                             data-filter="<?php echo $item['content_type']; ?>">
                            <img src="<?php echo getImageUrl($item['poster_path'], 'w500'); ?>" 
                                 alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                 loading="lazy">
                            <div class="card-overlay">
                                <div class="card-title"><?php echo htmlspecialchars($item['title']); ?></div>
                                <div class="card-info">
                                    <span><?php echo date('Y', strtotime($item['release_date'])); ?></span>
                                    <div class="rating">
                                        <span class="star">★</span>
                                        <span><?php echo $item['vote_average']; ?></span>
                                    </div>
                                </div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 5px;">
                                    Added: <?php echo date('M j, Y', strtotime($item['added_at'])); ?>
                                </div>
                            </div>
                            <button class="remove-btn" onclick="removeFromWatchlist(<?php echo $item['content_id']; ?>, '<?php echo $item['content_type']; ?>', this)">
                                ×
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-watchlist">
                    <h3>Your watchlist is empty</h3>
                    <p>Start adding movies and TV shows to your watchlist to see them here.</p>
                    <a href="movies.php" class="btn btn-primary">Browse Movies</a>
                    <a href="tv-shows.php" class="btn btn-secondary">Browse TV Shows</a>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Content Modal -->
    <div id="contentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-body">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        function filterWatchlist(type) {
            const cards = document.querySelectorAll('.content-card');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // Update active tab
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // Filter cards
            cards.forEach(card => {
                if (type === 'all' || card.dataset.filter === type) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        async function removeFromWatchlist(contentId, contentType, button) {
            if (!confirm('Remove this item from your watchlist?')) {
                return;
            }
            
            try {
                const response = await fetch('api/watchlist.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content_id: contentId,
                        content_type: contentType,
                        action: 'toggle'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Remove the card from DOM
                    const card = button.closest('.content-card');
                    card.style.opacity = '0';
                    setTimeout(() => {
                        card.remove();
                        
                        // Check if watchlist is empty
                        const remainingCards = document.querySelectorAll('.content-card');
                        if (remainingCards.length === 0) {
                            location.reload();
                        }
                    }, 300);
                } else {
                    alert('Failed to remove item: ' + data.message);
                }
            } catch (error) {
                alert('Error removing item: ' + error.message);
            }
        }
        
        // Search functionality
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.content-card');
            
            cards.forEach(card => {
                const title = card.querySelector('.card-title').textContent.toLowerCase();
                if (title.includes(query)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
