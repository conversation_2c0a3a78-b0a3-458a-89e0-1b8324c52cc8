-- StreamFlix Database Updates
-- Run this SQL file to update your database structure
-- Version: 2.0.0
-- Date: 2024-12-16

-- =====================================================
-- 1. CREATE EMBED SERVERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS embed_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    movie_url TEXT NOT NULL,
    tv_url TEXT NOT NULL,
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    last_tested TIMESTAMP NULL,
    last_response_time INT NULL,
    last_status_code INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_priority (priority),
    INDEX idx_active (is_active)
);

-- =====================================================
-- 2. CREATE SITE SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- =====================================================
-- 3. CREATE USER ACTIVITY TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
    content_type ENUM('movie', 'tv_show') NULL,
    content_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_activity (user_id, created_at),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. CREATE WATCHLIST TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS watchlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_watchlist (user_id, content_type, content_id),
    INDEX idx_user_watchlist (user_id)
);

-- =====================================================
-- 5. ADD MISSING COLUMNS TO EXISTING TABLES
-- =====================================================

-- Add columns to movies table (MySQL compatible)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'movies'
     AND column_name = 'created_at'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE movies ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'movies'
     AND column_name = 'is_featured'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE movies ADD COLUMN is_featured BOOLEAN DEFAULT 0',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'movies'
     AND column_name = 'is_trending'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE movies ADD COLUMN is_trending BOOLEAN DEFAULT 0',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add columns to tv_shows table (MySQL compatible)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'tv_shows'
     AND column_name = 'created_at'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE tv_shows ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'tv_shows'
     AND column_name = 'is_featured'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE tv_shows ADD COLUMN is_featured BOOLEAN DEFAULT 0',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'tv_shows'
     AND column_name = 'is_trending'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE tv_shows ADD COLUMN is_trending BOOLEAN DEFAULT 0',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add columns to users table (MySQL compatible)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND column_name = 'created_at'
     AND table_schema = DATABASE()) = 0,
    'ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
    'SELECT 1'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 6. CREATE PERFORMANCE INDEXES
-- =====================================================

-- Movies indexes
CREATE INDEX IF NOT EXISTS idx_movies_tmdb ON movies(tmdb_id);
CREATE INDEX IF NOT EXISTS idx_movies_featured ON movies(is_featured);
CREATE INDEX IF NOT EXISTS idx_movies_trending ON movies(is_trending);
CREATE INDEX IF NOT EXISTS idx_movies_created ON movies(created_at);
CREATE INDEX IF NOT EXISTS idx_movies_popularity ON movies(popularity);
CREATE INDEX IF NOT EXISTS idx_movies_rating ON movies(vote_average);

-- TV Shows indexes
CREATE INDEX IF NOT EXISTS idx_tv_tmdb ON tv_shows(tmdb_id);
CREATE INDEX IF NOT EXISTS idx_tv_featured ON tv_shows(is_featured);
CREATE INDEX IF NOT EXISTS idx_tv_trending ON tv_shows(is_trending);
CREATE INDEX IF NOT EXISTS idx_tv_created ON tv_shows(created_at);
CREATE INDEX IF NOT EXISTS idx_tv_popularity ON tv_shows(popularity);
CREATE INDEX IF NOT EXISTS idx_tv_rating ON tv_shows(vote_average);

-- Users indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_created ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Genres indexes
CREATE INDEX IF NOT EXISTS idx_genres_tmdb ON genres(tmdb_id);

-- =====================================================
-- 7. INSERT DEFAULT EMBED SERVERS
-- =====================================================
INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1, 1),
('VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2, 1),
('VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3, 1),
('SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4, 1),
('EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5, 1),
('LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6, 1);

-- =====================================================
-- 8. INSERT DEFAULT SITE SETTINGS
-- =====================================================
INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'StreamFlix', 'string', 'Website name'),
('site_description', 'Premium Movie & TV Show Streaming Platform', 'string', 'Website description'),
('tmdb_api_key', '', 'string', 'TMDB API Key for content import'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode'),
('registration_enabled', '1', 'boolean', 'Allow new user registrations'),
('featured_content_limit', '10', 'number', 'Number of featured content items'),
('trending_content_limit', '20', 'number', 'Number of trending content items'),
('max_servers_per_content', '6', 'number', 'Maximum embed servers per content'),
('cache_duration', '3600', 'number', 'Cache duration in seconds'),
('auto_import_trending', '0', 'boolean', 'Auto import trending content daily'),
('player_autoplay', '1', 'boolean', 'Enable autoplay in player'),
('show_ratings', '1', 'boolean', 'Show content ratings'),
('enable_comments', '0', 'boolean', 'Enable user comments'),
('enable_reviews', '0', 'boolean', 'Enable user reviews'),
('content_per_page', '24', 'number', 'Content items per page');

-- =====================================================
-- 9. UPDATE EXISTING DATA
-- =====================================================

-- Set created_at for existing records without it
UPDATE movies SET created_at = NOW() WHERE created_at IS NULL;
UPDATE tv_shows SET created_at = NOW() WHERE created_at IS NULL;
UPDATE users SET created_at = NOW() WHERE created_at IS NULL;

-- =====================================================
-- 10. CREATE ADDITIONAL USEFUL TABLES
-- =====================================================

-- Content views tracking
CREATE TABLE IF NOT EXISTS content_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    tmdb_id INT,
    view_duration INT DEFAULT 0,
    completed BOOLEAN DEFAULT 0,
    last_position INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_views (user_id, content_type, content_id),
    INDEX idx_content_views (content_type, content_id),
    INDEX idx_view_date (created_at)
);

-- User preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_pref (user_id, preference_key)
);

-- Content ratings by users
CREATE TABLE IF NOT EXISTS user_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    rating DECIMAL(2,1) NOT NULL CHECK (rating >= 0 AND rating <= 10),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_rating (user_id, content_type, content_id),
    INDEX idx_content_ratings (content_type, content_id),
    INDEX idx_rating_value (rating)
);

-- =====================================================
-- 11. CREATE VIEWS FOR ANALYTICS
-- =====================================================

-- Popular content view
CREATE OR REPLACE VIEW popular_content AS
SELECT 
    'movie' as content_type,
    id as content_id,
    tmdb_id,
    title as name,
    vote_average,
    popularity,
    created_at
FROM movies
WHERE vote_average > 7.0
UNION ALL
SELECT 
    'tv_show' as content_type,
    id as content_id,
    tmdb_id,
    name,
    vote_average,
    popularity,
    created_at
FROM tv_shows
WHERE vote_average > 7.0
ORDER BY popularity DESC;

-- Recent content view
CREATE OR REPLACE VIEW recent_content AS
SELECT 
    'movie' as content_type,
    id as content_id,
    tmdb_id,
    title as name,
    created_at
FROM movies
UNION ALL
SELECT 
    'tv_show' as content_type,
    id as content_id,
    tmdb_id,
    name,
    created_at
FROM tv_shows
ORDER BY created_at DESC;

-- =====================================================
-- 12. OPTIMIZE TABLES
-- =====================================================
OPTIMIZE TABLE movies;
OPTIMIZE TABLE tv_shows;
OPTIMIZE TABLE users;
OPTIMIZE TABLE genres;
OPTIMIZE TABLE movie_genres;
OPTIMIZE TABLE tv_show_genres;
OPTIMIZE TABLE seasons;
OPTIMIZE TABLE episodes;

-- =====================================================
-- UPDATE COMPLETE
-- =====================================================
-- Database structure updated successfully!
-- Version: 2.0.0
-- All new features are now supported.
