<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$movie_id = (int)($_GET['id'] ?? 0);
$message = '';
$error = '';

if ($movie_id <= 0) {
    redirectTo('movies.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = new Database();
        $conn = $db->connect();
        
        if ($action === 'update_movie') {
            $title = sanitizeInput($_POST['title'] ?? '');
            $overview = sanitizeInput($_POST['overview'] ?? '');
            $release_date = sanitizeInput($_POST['release_date'] ?? '');
            $vote_average = (float)($_POST['vote_average'] ?? 0);
            $is_featured = isset($_POST['is_featured']) ? 1 : 0;
            $is_trending = isset($_POST['is_trending']) ? 1 : 0;
            
            if (!empty($title)) {
                $stmt = $conn->prepare("
                    UPDATE movies 
                    SET title = ?, overview = ?, release_date = ?, vote_average = ?, 
                        is_featured = ?, is_trending = ?
                    WHERE id = ?
                ");
                $stmt->execute([$title, $overview, $release_date, $vote_average, $is_featured, $is_trending, $movie_id]);
                
                $message = "Movie updated successfully!";
            } else {
                $error = "Title is required.";
            }
        }
        
        if ($action === 'add_genre') {
            $genre_name = sanitizeInput($_POST['genre_name'] ?? '');
            
            if (!empty($genre_name)) {
                // Create genre if it doesn't exist
                $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
                $stmt->execute([$genre_name, 99999 + rand(1, 1000)]);
                
                // Get genre ID
                $stmt = $conn->prepare("SELECT id FROM genres WHERE name = ?");
                $stmt->execute([$genre_name]);
                $genre = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($genre) {
                    // Add to movie_genres
                    $stmt = $conn->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                    $stmt->execute([$movie_id, $genre['id']]);
                    $message = "Genre '{$genre_name}' added successfully!";
                }
            }
        }
        
        if ($action === 'remove_genre') {
            $genre_id = (int)($_POST['genre_id'] ?? 0);
            
            if ($genre_id > 0) {
                $stmt = $conn->prepare("DELETE FROM movie_genres WHERE movie_id = ? AND genre_id = ?");
                $stmt->execute([$movie_id, $genre_id]);
                $message = "Genre removed successfully!";
            }
        }
        
    } catch (Exception $e) {
        $error = 'Error: ' . $e->getMessage();
    }
}

// Get movie details
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stmt = $conn->prepare("SELECT * FROM movies WHERE id = ?");
    $stmt->execute([$movie_id]);
    $movie = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$movie) {
        redirectTo('movies.php');
    }
    
    // Get movie genres
    $stmt = $conn->prepare("
        SELECT g.id, g.name 
        FROM movie_genres mg 
        JOIN genres g ON mg.genre_id = g.id 
        WHERE mg.movie_id = ?
    ");
    $stmt->execute([$movie_id]);
    $movie_genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    redirectTo('movies.php');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Movie - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .edit-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .movie-poster {
            width: 200px;
            height: 300px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .genre-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .genre-tag {
            background: var(--primary-color);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .genre-remove {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            line-height: 1;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>✏️ Edit Movie</h1>
            <p>Edit movie details, genres, and settings</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="edit-form">
            <div style="display: flex; gap: 30px; margin-bottom: 30px;">
                <div>
                    <img src="<?php echo getImageUrl($movie['poster_path'], 'w300'); ?>" 
                         alt="<?php echo htmlspecialchars($movie['title']); ?>" 
                         class="movie-poster">
                </div>
                
                <div style="flex: 1;">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_movie">
                        
                        <div class="form-group">
                            <label for="title">Title</label>
                            <input type="text" id="title" name="title" 
                                   value="<?php echo htmlspecialchars($movie['title']); ?>" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="release_date">Release Date</label>
                                <input type="date" id="release_date" name="release_date" 
                                       value="<?php echo $movie['release_date']; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="vote_average">Rating</label>
                                <input type="number" id="vote_average" name="vote_average" 
                                       step="0.1" min="0" max="10"
                                       value="<?php echo $movie['vote_average']; ?>">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="overview">Overview</label>
                            <textarea id="overview" name="overview"><?php echo htmlspecialchars($movie['overview']); ?></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="is_featured" name="is_featured" 
                                           <?php echo $movie['is_featured'] ? 'checked' : ''; ?>>
                                    <label for="is_featured">Featured Movie</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="is_trending" name="is_trending" 
                                           <?php echo $movie['is_trending'] ? 'checked' : ''; ?>>
                                    <label for="is_trending">Trending Movie</label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Movie</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Genres Section -->
        <div class="edit-form">
            <h3>🏷️ Genres</h3>
            
            <div class="genre-list">
                <?php foreach ($movie_genres as $genre): ?>
                    <div class="genre-tag">
                        <?php echo htmlspecialchars($genre['name']); ?>
                        <form method="POST" style="display: inline;" onsubmit="return confirm('Remove this genre?')">
                            <input type="hidden" name="action" value="remove_genre">
                            <input type="hidden" name="genre_id" value="<?php echo $genre['id']; ?>">
                            <button type="submit" class="genre-remove">×</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <form method="POST" style="display: flex; gap: 10px; align-items: end;">
                <input type="hidden" name="action" value="add_genre">
                <div class="form-group" style="flex: 1; margin-bottom: 0;">
                    <label for="genre_name">Add Genre</label>
                    <input type="text" id="genre_name" name="genre_name" 
                           placeholder="e.g., Action, Comedy, Anime, Hentai" required>
                </div>
                <button type="submit" class="btn btn-secondary">Add Genre</button>
            </form>
        </div>

        <!-- Movie Info -->
        <div class="edit-form">
            <h3>📊 Movie Information</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>TMDB ID:</strong> <?php echo $movie['tmdb_id']; ?>
                </div>
                <div>
                    <strong>Runtime:</strong> <?php echo $movie['runtime'] ?? 'N/A'; ?> minutes
                </div>
                <div>
                    <strong>Language:</strong> <?php echo strtoupper($movie['original_language'] ?? 'N/A'); ?>
                </div>
                <div>
                    <strong>Added:</strong> <?php echo date('M j, Y', strtotime($movie['created_at'])); ?>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="movies.php" class="btn btn-secondary">← Back to Movies</a>
            <a href="../player.php?id=<?php echo $movie['tmdb_id']; ?>&type=movie" class="btn btn-primary" target="_blank">▶ Watch Movie</a>
        </div>
    </div>
</body>
</html>
