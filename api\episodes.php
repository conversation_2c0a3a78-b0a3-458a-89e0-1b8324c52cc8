<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

$tv_show_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$season_number = isset($_GET['season']) ? (int)$_GET['season'] : 1;

if (!$tv_show_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid TV show ID']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get episodes for the specified season
    $stmt = $conn->prepare("
        SELECT e.*, s.season_number
        FROM episodes e
        JOIN seasons s ON e.season_id = s.id
        WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season_number
        ORDER BY e.episode_number
    ");
    $stmt->execute([
        ':tv_show_id' => $tv_show_id,
        ':season_number' => $season_number
    ]);
    $episodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'episodes' => $episodes
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
