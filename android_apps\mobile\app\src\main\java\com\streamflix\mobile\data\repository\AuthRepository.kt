package com.streamflix.mobile.data.repository

import com.streamflix.mobile.data.local.PreferencesManager
import com.streamflix.mobile.data.remote.api.AuthApiService
import com.streamflix.mobile.data.remote.dto.LoginRequest
import com.streamflix.mobile.data.remote.dto.RegisterRequest
import com.streamflix.mobile.data.remote.dto.AuthResponse
import com.streamflix.mobile.domain.model.User
import com.streamflix.mobile.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val authApiService: AuthApiService,
    private val preferencesManager: PreferencesManager
) {

    suspend fun login(username: String, password: String): Flow<Resource<User>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = authApiService.login(LoginRequest(username, password))
            
            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!
                
                // Save token and user data
                preferencesManager.saveAuthToken(authResponse.token)
                preferencesManager.saveUserData(authResponse.user)
                
                emit(Resource.Success(authResponse.user))
            } else {
                emit(Resource.Error("Login failed: ${response.message()}"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }

    suspend fun register(
        username: String,
        email: String,
        password: String,
        fullName: String
    ): Flow<Resource<User>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = authApiService.register(
                RegisterRequest(username, email, password, fullName)
            )
            
            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!
                
                // Save token and user data
                preferencesManager.saveAuthToken(authResponse.token)
                preferencesManager.saveUserData(authResponse.user)
                
                emit(Resource.Success(authResponse.user))
            } else {
                emit(Resource.Error("Registration failed: ${response.message()}"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }

    suspend fun logout(): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            // Call logout API
            val response = authApiService.logout()
            
            // Clear local data regardless of API response
            preferencesManager.clearAuthData()
            
            emit(Resource.Success(true))
            
        } catch (e: Exception) {
            // Clear local data even if API call fails
            preferencesManager.clearAuthData()
            emit(Resource.Success(true))
        }
    }

    suspend fun isUserLoggedIn(): Boolean {
        val token = preferencesManager.getAuthToken()
        return !token.isNullOrEmpty()
    }

    suspend fun getCurrentUser(): User? {
        return preferencesManager.getUserData()
    }

    suspend fun refreshToken(): Flow<Resource<String>> = flow {
        try {
            emit(Resource.Loading())
            
            val currentToken = preferencesManager.getAuthToken()
            if (currentToken.isNullOrEmpty()) {
                emit(Resource.Error("No token available"))
                return@flow
            }
            
            val response = authApiService.refreshToken()
            
            if (response.isSuccessful && response.body() != null) {
                val newToken = response.body()!!.token
                preferencesManager.saveAuthToken(newToken)
                emit(Resource.Success(newToken))
            } else {
                emit(Resource.Error("Token refresh failed"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }

    suspend fun updateProfile(user: User): Flow<Resource<User>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = authApiService.updateProfile(user)
            
            if (response.isSuccessful && response.body() != null) {
                val updatedUser = response.body()!!
                preferencesManager.saveUserData(updatedUser)
                emit(Resource.Success(updatedUser))
            } else {
                emit(Resource.Error("Profile update failed"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }

    suspend fun changePassword(
        currentPassword: String,
        newPassword: String
    ): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = authApiService.changePassword(
                mapOf(
                    "current_password" to currentPassword,
                    "new_password" to newPassword
                )
            )
            
            if (response.isSuccessful) {
                emit(Resource.Success(true))
            } else {
                emit(Resource.Error("Password change failed"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }

    suspend fun forgotPassword(email: String): Flow<Resource<Boolean>> = flow {
        try {
            emit(Resource.Loading())
            
            val response = authApiService.forgotPassword(mapOf("email" to email))
            
            if (response.isSuccessful) {
                emit(Resource.Success(true))
            } else {
                emit(Resource.Error("Failed to send reset email"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("Network error: ${e.localizedMessage}"))
        }
    }
}
