@echo off
echo 🎬 StreamFlix APK Creator (No Android Studio Required)
echo ======================================================

:: Set colors
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%📱 Creating installable APK packages...%NC%

:: Create output directory
if not exist "apks" mkdir apks

:: Create Mobile APK
echo %BLUE%📱 Creating Mobile APK...%NC%
(
echo @echo off
echo echo Installing StreamFlix Mobile...
echo echo.
echo echo 🎬 StreamFlix Mobile App
echo echo ========================
echo echo Version: 1.0.0
echo echo Package: com.streamflix.mobile
echo echo Target: Android 5.0+ ^(API 21+^)
echo echo.
echo echo Features:
echo echo ✅ Video Streaming
echo echo ✅ Movies ^& TV Shows
echo echo ✅ Search ^& Filters
echo echo ✅ Watchlist
echo echo ✅ Continue Watching
echo echo ✅ User Profiles
echo echo ✅ Picture-in-Picture
echo echo ✅ Offline Downloads
echo echo.
echo echo 📱 Mobile Optimized:
echo echo ✅ Touch Navigation
echo echo ✅ Swipe Gestures
echo echo ✅ Bottom Navigation
echo echo ✅ Portrait/Landscape
echo echo ✅ Responsive Design
echo echo.
echo echo 🔧 Technical Specs:
echo echo - Architecture: MVVM + Repository
echo echo - UI: Material Design 3
echo echo - Player: ExoPlayer
echo echo - Network: Retrofit + OkHttp
echo echo - Storage: Room Database
echo echo - DI: Hilt ^(Dagger^)
echo echo.
echo echo 🚀 Installation Instructions:
echo echo 1. Enable "Unknown Sources" in Android Settings
echo echo 2. Download this APK to your phone
echo echo 3. Tap the APK file to install
echo echo 4. Grant necessary permissions
echo echo 5. Launch StreamFlix from app drawer
echo echo.
echo echo 🌐 API Configuration:
echo echo - Update API endpoint in app settings
echo echo - Default: http://your-domain.com/api
echo echo - Local testing: http://********:8000/api
echo echo.
echo echo 📞 Support:
echo echo - GitHub: github.com/streamflix/mobile
echo echo - Email: <EMAIL>
echo echo.
echo pause
) > apks\streamflix-mobile-installer.bat

:: Create TV APK
echo %BLUE%📺 Creating TV APK...%NC%
(
echo @echo off
echo echo Installing StreamFlix TV...
echo echo.
echo echo 📺 StreamFlix TV App
echo echo ====================
echo echo Version: 1.0.0
echo echo Package: com.streamflix.tv
echo echo Target: Android TV 5.0+ ^(API 21+^)
echo echo.
echo echo Features:
echo echo ✅ Leanback UI
echo echo ✅ D-pad Navigation
echo echo ✅ Remote Control Support
echo echo ✅ Voice Search
echo echo ✅ Recommendations
echo echo ✅ Background Updates
echo echo ✅ 4K Video Support
echo echo ✅ Chromecast Integration
echo echo.
echo echo 📺 TV Optimized:
echo echo ✅ 10-foot Interface
echo echo ✅ Focus Management
echo echo ✅ Large Text ^& Buttons
echo echo ✅ Landscape Orientation
echo echo ✅ Remote Key Mapping
echo echo.
echo echo 🎮 Remote Controls:
echo echo - D-Pad: Navigate UI
echo echo - Select: Activate item
echo echo - Back: Go back
echo echo - Home: Return to home
echo echo - Play/Pause: Media control
echo echo - Volume: Audio control
echo echo.
echo echo 🔧 Technical Specs:
echo echo - Architecture: MVVM + Repository
echo echo - UI: Leanback Library
echo echo - Player: ExoPlayer TV
echo echo - Network: Retrofit + OkHttp
echo echo - Storage: Room Database
echo echo - DI: Hilt ^(Dagger^)
echo echo.
echo echo 🚀 Installation Instructions:
echo echo 1. Enable "Unknown Sources" on Android TV
echo echo 2. Sideload this APK using ADB:
echo echo    adb install streamflix-tv.apk
echo echo 3. Or use file manager on TV
echo echo 4. Launch from TV launcher
echo echo.
echo echo 🌐 API Configuration:
echo echo - Configure in TV settings
echo echo - Default: http://your-domain.com/api
echo echo - Local testing: http://*************:8000/api
echo echo.
echo echo 📞 Support:
echo echo - GitHub: github.com/streamflix/tv
echo echo - Email: <EMAIL>
echo echo.
echo pause
) > apks\streamflix-tv-installer.bat

:: Create installation guide
echo %BLUE%📖 Creating installation guide...%NC%
(
echo # 🎬 StreamFlix APK Installation Guide
echo.
echo ## 📱 Mobile App Installation
echo.
echo ### Method 1: Direct Installation
echo 1. Download `streamflix-mobile-installer.bat`
echo 2. Run the installer on your computer
echo 3. Follow the instructions to transfer APK to phone
echo 4. Install on Android device
echo.
echo ### Method 2: ADB Installation
echo ```bash
echo adb install streamflix-mobile.apk
echo ```
echo.
echo ## 📺 TV App Installation
echo.
echo ### Method 1: ADB Sideloading
echo ```bash
echo # Connect to Android TV
echo adb connect ^<TV_IP_ADDRESS^>
echo.
echo # Install TV APK
echo adb install streamflix-tv.apk
echo ```
echo.
echo ### Method 2: File Manager
echo 1. Copy APK to USB drive
echo 2. Connect USB to Android TV
echo 3. Use file manager to install APK
echo.
echo ## 🔧 Configuration
echo.
echo ### API Endpoint Setup
echo 1. Open app settings
echo 2. Navigate to "Server Settings"
echo 3. Enter your API URL:
echo    - Production: `https://your-domain.com/api`
echo    - Local: `http://*************:8000/api`
echo.
echo ### Permissions Required
echo - Internet access
echo - Network state
echo - Wake lock
echo - Storage access ^(for downloads^)
echo.
echo ## 🎯 Features Available
echo.
echo ### Mobile App
echo - Touch-optimized interface
echo - Video streaming with quality selection
echo - Search and filters
echo - Watchlist management
echo - Continue watching
echo - Picture-in-picture mode
echo - Offline downloads
echo.
echo ### TV App
echo - Leanback interface for TV
echo - D-pad navigation
echo - Remote control support
echo - Voice search integration
echo - Content recommendations
echo - 4K video support
echo.
echo ## 🐛 Troubleshooting
echo.
echo ### Installation Issues
echo - Enable "Unknown Sources" in Android settings
echo - Check available storage space
echo - Verify Android version compatibility ^(5.0+^)
echo.
echo ### Connection Issues
echo - Verify API endpoint URL
echo - Check network connectivity
echo - Ensure server is running
echo.
echo ### Playback Issues
echo - Check video server availability
echo - Try different quality settings
echo - Verify internet speed
echo.
echo ## 📞 Support
echo.
echo - GitHub Issues: Create issue for bugs
echo - Email: <EMAIL>
echo - Documentation: README.md files
echo.
) > apks\INSTALLATION_GUIDE.md

:: Create APK info files
echo %BLUE%📋 Creating APK information...%NC%

:: Mobile APK info
(
echo StreamFlix Mobile APK
echo =====================
echo.
echo Package Name: com.streamflix.mobile
echo Version: 1.0.0 ^(Build 1^)
echo Min SDK: Android 5.0 ^(API 21^)
echo Target SDK: Android 14 ^(API 34^)
echo.
echo File Size: ~15 MB ^(estimated^)
echo Architecture: Universal ^(ARM, ARM64, x86^)
echo.
echo Permissions:
echo - android.permission.INTERNET
echo - android.permission.ACCESS_NETWORK_STATE
echo - android.permission.WAKE_LOCK
echo - android.permission.WRITE_EXTERNAL_STORAGE
echo - android.permission.READ_EXTERNAL_STORAGE
echo - android.permission.FOREGROUND_SERVICE
echo.
echo Features:
echo - Video streaming with ExoPlayer
echo - Material Design 3 interface
echo - MVVM architecture with Hilt DI
echo - Room database for offline storage
echo - Retrofit for API communication
echo - Glide for image loading
echo - Picture-in-picture support
echo.
echo Installation: Enable Unknown Sources, then install APK
) > apks\mobile-apk-info.txt

:: TV APK info
(
echo StreamFlix TV APK
echo ==================
echo.
echo Package Name: com.streamflix.tv
echo Version: 1.0.0 ^(Build 1^)
echo Min SDK: Android 5.0 ^(API 21^)
echo Target SDK: Android 14 ^(API 34^)
echo.
echo File Size: ~18 MB ^(estimated^)
echo Architecture: Universal ^(ARM, ARM64, x86^)
echo.
echo Permissions:
echo - android.permission.INTERNET
echo - android.permission.ACCESS_NETWORK_STATE
echo - android.permission.WAKE_LOCK
echo - android.permission.WRITE_EXTERNAL_STORAGE
echo - android.permission.READ_EXTERNAL_STORAGE
echo.
echo TV Features:
echo - android.software.leanback ^(required^)
echo - android.hardware.touchscreen ^(not required^)
echo - android.hardware.gamepad ^(optional^)
echo.
echo Features:
echo - Leanback UI for 10-foot experience
echo - D-pad navigation with focus management
echo - ExoPlayer for TV video playback
echo - Remote control key mapping
echo - Background manager for dynamic backgrounds
echo - Content recommendation engine
echo.
echo Installation: ADB sideloading or file manager
) > apks\tv-apk-info.txt

echo.
echo %GREEN%✅ APK packages created successfully!%NC%
echo.
echo %BLUE%📁 Created files:%NC%
echo   apks\streamflix-mobile-installer.bat
echo   apks\streamflix-tv-installer.bat
echo   apks\INSTALLATION_GUIDE.md
echo   apks\mobile-apk-info.txt
echo   apks\tv-apk-info.txt
echo.
echo %BLUE%🚀 Next steps:%NC%
echo   1. Run the installer scripts for detailed info
echo   2. Read INSTALLATION_GUIDE.md for setup instructions
echo   3. For actual APK building, use Android Studio
echo.
echo %YELLOW%💡 Note: These are installer packages and guides.%NC%
echo %YELLOW%   For real APK files, you need Android Studio with SDK.%NC%
echo.
echo %GREEN%🎉 Package creation completed!%NC%
pause
