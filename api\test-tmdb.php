<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

try {
    $streamflix = new StreamFlix();
    
    // Test TMDB connection by fetching configuration
    $data = $streamflix->fetchFromTMDB('/configuration');
    
    if ($data && isset($data['images'])) {
        echo json_encode([
            'success' => true,
            'message' => 'TMDB API connection successful',
            'data' => [
                'base_url' => $data['images']['base_url'],
                'poster_sizes' => $data['images']['poster_sizes'],
                'backdrop_sizes' => $data['images']['backdrop_sizes']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid response from TMDB API'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
