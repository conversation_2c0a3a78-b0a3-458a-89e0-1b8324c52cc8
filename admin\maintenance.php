<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

// Handle maintenance actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'backup_database') {
        try {
            $backup_file = createDatabaseBackup();
            $message = "Database backup created successfully: {$backup_file}";
        } catch (Exception $e) {
            $error = 'Backup failed: ' . $e->getMessage();
        }
    }
    
    if ($action === 'optimize_database') {
        try {
            $optimized = optimizeDatabase();
            $message = "Database optimized successfully. {$optimized} tables optimized.";
        } catch (Exception $e) {
            $error = 'Optimization failed: ' . $e->getMessage();
        }
    }
    
    if ($action === 'clear_cache') {
        try {
            $cleared = clearCache();
            $message = "Cache cleared successfully. {$cleared} files removed.";
        } catch (Exception $e) {
            $error = 'Cache clear failed: ' . $e->getMessage();
        }
    }
    
    if ($action === 'clean_orphaned') {
        try {
            $cleaned = cleanOrphanedData();
            $message = "Orphaned data cleaned. {$cleaned} records removed.";
        } catch (Exception $e) {
            $error = 'Cleanup failed: ' . $e->getMessage();
        }
    }
    
    if ($action === 'update_metadata') {
        try {
            $updated = updateContentMetadata();
            $message = "Metadata updated for {$updated} items.";
        } catch (Exception $e) {
            $error = 'Metadata update failed: ' . $e->getMessage();
        }
    }
}

function createDatabaseBackup() {
    $db = new Database();
    $conn = $db->connect();
    
    $backup_dir = __DIR__ . '/../backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $filename = 'streamflix_backup_' . date('Y-m-d_H-i-s') . '.sql';
    $filepath = $backup_dir . '/' . $filename;
    
    $tables = [];
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    $backup_content = "-- StreamFlix Database Backup\n";
    $backup_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
    $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
    
    foreach ($tables as $table) {
        // Get table structure
        $stmt = $conn->query("SHOW CREATE TABLE `{$table}`");
        $row = $stmt->fetch(PDO::FETCH_NUM);
        $backup_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
        $backup_content .= $row[1] . ";\n\n";
        
        // Get table data
        $stmt = $conn->query("SELECT * FROM `{$table}`");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns = array_keys($row);
            $values = array_map(function($value) use ($conn) {
                return $value === null ? 'NULL' : $conn->quote($value);
            }, array_values($row));
            
            $backup_content .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
        }
        $backup_content .= "\n";
    }
    
    $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
    
    file_put_contents($filepath, $backup_content);
    
    return $filename;
}

function optimizeDatabase() {
    $db = new Database();
    $conn = $db->connect();
    
    $tables = [];
    $stmt = $conn->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    $optimized = 0;
    foreach ($tables as $table) {
        $conn->exec("OPTIMIZE TABLE `{$table}`");
        $optimized++;
    }
    
    return $optimized;
}

function clearCache() {
    $cache_dirs = [
        __DIR__ . '/../cache',
        __DIR__ . '/../tmp',
        __DIR__ . '/../assets/cache'
    ];
    
    $cleared = 0;
    foreach ($cache_dirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                    $cleared++;
                }
            }
        }
    }
    
    return $cleared;
}

function cleanOrphanedData() {
    $db = new Database();
    $conn = $db->connect();
    
    $cleaned = 0;
    
    // Clean orphaned movie genres
    $stmt = $conn->prepare("DELETE mg FROM movie_genres mg LEFT JOIN movies m ON mg.movie_id = m.id WHERE m.id IS NULL");
    $stmt->execute();
    $cleaned += $stmt->rowCount();
    
    // Clean orphaned TV show genres
    $stmt = $conn->prepare("DELETE tg FROM tv_show_genres tg LEFT JOIN tv_shows t ON tg.tv_show_id = t.id WHERE t.id IS NULL");
    $stmt->execute();
    $cleaned += $stmt->rowCount();
    
    // Clean orphaned seasons
    $stmt = $conn->prepare("DELETE s FROM seasons s LEFT JOIN tv_shows t ON s.tv_show_id = t.id WHERE t.id IS NULL");
    $stmt->execute();
    $cleaned += $stmt->rowCount();
    
    // Clean orphaned episodes
    $stmt = $conn->prepare("DELETE e FROM episodes e LEFT JOIN seasons s ON e.season_id = s.id WHERE s.id IS NULL");
    $stmt->execute();
    $cleaned += $stmt->rowCount();
    
    return $cleaned;
}

function updateContentMetadata() {
    $streamflix = new StreamFlix();
    $db = new Database();
    $conn = $db->connect();
    
    $updated = 0;
    
    // Update movies with missing data
    $stmt = $conn->query("SELECT tmdb_id FROM movies WHERE (poster_path IS NULL OR poster_path = '') AND tmdb_id IS NOT NULL LIMIT 50");
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($movies as $movie) {
        try {
            $streamflix->importMovie($movie['tmdb_id']);
            $updated++;
            usleep(200000); // 0.2 second delay
        } catch (Exception $e) {
            continue;
        }
    }
    
    // Update TV shows with missing data
    $stmt = $conn->query("SELECT tmdb_id FROM tv_shows WHERE (poster_path IS NULL OR poster_path = '') AND tmdb_id IS NOT NULL LIMIT 50");
    $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($tv_shows as $show) {
        try {
            $streamflix->importTVShow($show['tmdb_id']);
            $updated++;
            usleep(200000); // 0.2 second delay
        } catch (Exception $e) {
            continue;
        }
    }
    
    return $updated;
}

// Get system information
try {
    $db = new Database();
    $conn = $db->connect();
    
    $system_info = [];
    
    // Database size
    $stmt = $conn->query("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    $system_info['db_size'] = $stmt->fetch(PDO::FETCH_ASSOC)['db_size_mb'] . ' MB';
    
    // Table counts
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies");
    $system_info['movies'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows");
    $system_info['tv_shows'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $system_info['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE is_active = 1");
    $system_info['active_servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Recent backups
    $backup_dir = __DIR__ . '/../backups';
    $backups = [];
    if (is_dir($backup_dir)) {
        $files = glob($backup_dir . '/streamflix_backup_*.sql');
        rsort($files);
        foreach (array_slice($files, 0, 5) as $file) {
            $backups[] = [
                'name' => basename($file),
                'size' => round(filesize($file) / 1024 / 1024, 2) . ' MB',
                'date' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
    }
    $system_info['backups'] = $backups;
    
} catch (Exception $e) {
    $system_info = [];
    $error = 'Failed to load system information: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .maintenance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .maintenance-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .action-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .action-item:last-child {
            border-bottom: none;
        }
        
        .action-info h4 {
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .action-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #000;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .system-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .backup-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .backup-item:last-child {
            border-bottom: none;
        }
        
        .backup-info {
            flex: 1;
        }
        
        .backup-name {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .backup-meta {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .maintenance-grid {
                grid-template-columns: 1fr;
            }
            
            .system-stats {
                grid-template-columns: 1fr;
            }
            
            .action-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>System Maintenance</h1>
            <p>Maintain and optimize your StreamFlix platform</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php" class="active">Maintenance</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <div class="maintenance-grid">
            <!-- System Overview -->
            <div class="maintenance-section">
                <h2 class="section-title">System Overview</h2>
                <div class="system-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $system_info['movies'] ?? 0; ?></div>
                        <div class="stat-label">Movies</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $system_info['tv_shows'] ?? 0; ?></div>
                        <div class="stat-label">TV Shows</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $system_info['users'] ?? 0; ?></div>
                        <div class="stat-label">Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $system_info['db_size'] ?? 'N/A'; ?></div>
                        <div class="stat-label">Database Size</div>
                    </div>
                </div>
            </div>

            <!-- Database Maintenance -->
            <div class="maintenance-section">
                <h2 class="section-title">Database Maintenance</h2>
                
                <div class="action-item">
                    <div class="action-info">
                        <h4>Create Backup</h4>
                        <p>Create a full database backup for safety</p>
                    </div>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="backup_database">
                        <button type="submit" class="action-btn btn-primary">Backup</button>
                    </form>
                </div>
                
                <div class="action-item">
                    <div class="action-info">
                        <h4>Optimize Database</h4>
                        <p>Optimize all database tables for better performance</p>
                    </div>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="optimize_database">
                        <button type="submit" class="action-btn btn-secondary">Optimize</button>
                    </form>
                </div>
                
                <div class="action-item">
                    <div class="action-info">
                        <h4>Clean Orphaned Data</h4>
                        <p>Remove orphaned records and broken references</p>
                    </div>
                    <form method="POST" style="display: inline;" onsubmit="return confirm('This will remove orphaned data. Continue?')">
                        <input type="hidden" name="action" value="clean_orphaned">
                        <button type="submit" class="action-btn btn-warning">Clean</button>
                    </form>
                </div>
            </div>

            <!-- Content Maintenance -->
            <div class="maintenance-section">
                <h2 class="section-title">Content Maintenance</h2>
                
                <div class="action-item">
                    <div class="action-info">
                        <h4>Update Metadata</h4>
                        <p>Refresh missing posters and metadata from TMDB</p>
                    </div>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="update_metadata">
                        <button type="submit" class="action-btn btn-primary">Update</button>
                    </form>
                </div>
                
                <div class="action-item">
                    <div class="action-info">
                        <h4>Clear Cache</h4>
                        <p>Clear temporary files and cached data</p>
                    </div>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_cache">
                        <button type="submit" class="action-btn btn-secondary">Clear</button>
                    </form>
                </div>
            </div>

            <!-- Recent Backups -->
            <div class="maintenance-section">
                <h2 class="section-title">Recent Backups</h2>
                
                <?php if (empty($system_info['backups'])): ?>
                    <p style="color: var(--text-secondary);">No backups found. Create your first backup above.</p>
                <?php else: ?>
                    <div class="backup-list">
                        <?php foreach ($system_info['backups'] as $backup): ?>
                            <div class="backup-item">
                                <div class="backup-info">
                                    <div class="backup-name"><?php echo htmlspecialchars($backup['name']); ?></div>
                                    <div class="backup-meta"><?php echo $backup['size']; ?> • <?php echo $backup['date']; ?></div>
                                </div>
                                <a href="../backups/<?php echo htmlspecialchars($backup['name']); ?>" 
                                   class="action-btn btn-secondary" download>Download</a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Add confirmation for potentially destructive actions
        document.querySelectorAll('form').forEach(form => {
            const action = form.querySelector('input[name="action"]')?.value;
            
            if (['optimize_database', 'clear_cache', 'update_metadata'].includes(action)) {
                form.addEventListener('submit', function(e) {
                    if (!confirm('Are you sure you want to perform this action?')) {
                        e.preventDefault();
                    }
                });
            }
        });
    </script>
</body>
</html>
