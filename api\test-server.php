<?php
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$server_id = (int)($input['server_id'] ?? 0);
$test_id = (int)($input['test_id'] ?? 550); // Default to Blade Runner

if (!$server_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid server ID']);
    exit();
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get server details
    $stmt = $conn->prepare("SELECT * FROM embed_servers WHERE id = :id");
    $stmt->execute([':id' => $server_id]);
    $server = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$server) {
        echo json_encode(['success' => false, 'message' => 'Server not found']);
        exit();
    }
    
    // Test movie URL
    $test_url = str_replace('{id}', $test_id, $server['movie_url']);
    
    $start_time = microtime(true);
    
    // Create context with timeout and user agent
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'follow_location' => true,
            'max_redirects' => 3
        ]
    ]);
    
    // Test the URL
    $headers = @get_headers($test_url, 1, $context);
    $end_time = microtime(true);
    
    $response_time = round(($end_time - $start_time) * 1000); // Convert to milliseconds
    
    if ($headers === false) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to connect to server',
            'test_url' => $test_url,
            'response_time' => $response_time
        ]);
        exit();
    }
    
    // Check response code
    $response_code = 0;
    if (isset($headers[0])) {
        preg_match('/HTTP\/\d\.\d\s+(\d+)/', $headers[0], $matches);
        $response_code = isset($matches[1]) ? (int)$matches[1] : 0;
    }
    
    $success = $response_code >= 200 && $response_code < 400;
    
    // Update server test results
    $stmt = $conn->prepare("
        UPDATE embed_servers 
        SET last_tested = NOW(), 
            last_response_time = :response_time,
            last_status_code = :status_code
        WHERE id = :id
    ");
    $stmt->execute([
        ':response_time' => $response_time,
        ':status_code' => $response_code,
        ':id' => $server_id
    ]);
    
    echo json_encode([
        'success' => $success,
        'message' => $success ? 'Server is responding' : 'Server returned error code: ' . $response_code,
        'response_code' => $response_code,
        'response_time' => $response_time,
        'test_url' => $test_url
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
