<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get all genres with content count
    $stmt = $conn->query("
        SELECT 
            g.*,
            (SELECT COUNT(*) FROM movie_genres mg WHERE mg.genre_id = g.id) as movie_count,
            (SELECT COUNT(*) FROM tv_show_genres tg WHERE tg.genre_id = g.id) as tv_show_count
        FROM genres g
        HAVING (movie_count > 0 OR tv_show_count > 0)
        ORDER BY g.name
    ");
    $genres = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $genres = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genres - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .page-header {
            margin: 100px 0 30px;
            text-align: center;
        }
        
        .genres-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .genre-card {
            background: var(--secondary-color);
            border-radius: 8px;
            padding: 30px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .genre-card:hover {
            transform: translateY(-5px);
            border-color: var(--primary-color);
            box-shadow: 0 10px 30px rgba(229, 9, 20, 0.2);
        }
        
        .genre-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 15px;
        }
        
        .genre-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .genre-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .genre-btn {
            padding: 8px 16px;
            background: var(--dark-bg);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .genre-btn:hover {
            background: var(--primary-color);
        }
        
        .no-genres {
            text-align: center;
            padding: 60px 20px;
            background: var(--secondary-color);
            border-radius: 8px;
        }
        
        .no-genres h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
        }
        
        .no-genres p {
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
        
        .popular-genres {
            margin-bottom: 40px;
        }
        
        .popular-genres h2 {
            margin-bottom: 20px;
            color: var(--text-primary);
        }
        
        .genre-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .genre-tag {
            padding: 8px 16px;
            background: var(--dark-bg);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .genre-tag:hover {
            background: var(--primary-color);
            transform: scale(1.05);
        }
        
        @media (max-width: 768px) {
            .genres-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .genre-card {
                padding: 20px 15px;
            }
            
            .genre-name {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
            
            <ul class="nav-links">
                <li><a href="index.php">Home</a></li>
                <li><a href="movies.php">Movies</a></li>
                <li><a href="tv-shows.php">TV Shows</a></li>
                <li><a href="genres.php" class="active">Genres</a></li>
                <?php if (isLoggedIn()): ?>
                    <li><a href="watchlist.php">My List</a></li>
                <?php endif; ?>
            </ul>
            
            <div class="user-menu">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search genres...">
                    <button class="search-btn">🔍</button>
                    <div class="search-results"></div>
                </div>
                
                <?php if (isLoggedIn()): ?>
                    <div class="user-dropdown">
                        <button class="user-btn"><?php echo $_SESSION['username']; ?></button>
                        <div class="dropdown-menu">
                            <a href="profile.php">Profile</a>
                            <a href="watchlist.php">My List</a>
                            <?php if (isAdmin()): ?>
                                <a href="admin/">Admin Panel</a>
                            <?php endif; ?>
                            <a href="logout.php">Logout</a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary">Sign In</a>
                <?php endif; ?>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-section">
            <div class="page-header">
                <h1>Browse by Genre</h1>
                <p>Discover content by your favorite genres</p>
            </div>
            
            <?php if (!empty($genres)): ?>
                <!-- Popular Genres (Top 10) -->
                <?php 
                $popular_genres = array_slice($genres, 0, 10);
                if (!empty($popular_genres)): 
                ?>
                <div class="popular-genres">
                    <h2>Popular Genres</h2>
                    <div class="genre-tags">
                        <?php foreach ($popular_genres as $genre): ?>
                            <a href="movies.php?genre=<?php echo $genre['id']; ?>" class="genre-tag">
                                <?php echo htmlspecialchars($genre['name']); ?>
                                (<?php echo $genre['movie_count'] + $genre['tv_show_count']; ?>)
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- All Genres Grid -->
                <h2 style="margin-bottom: 20px; color: var(--text-primary);">All Genres</h2>
                <div class="genres-grid">
                    <?php foreach ($genres as $genre): ?>
                        <div class="genre-card" onclick="window.location.href='movies.php?genre=<?php echo $genre['id']; ?>'">
                            <div class="genre-name"><?php echo htmlspecialchars($genre['name']); ?></div>
                            
                            <div class="genre-stats">
                                <div class="stat">
                                    <div class="stat-number"><?php echo $genre['movie_count']; ?></div>
                                    <div class="stat-label">Movies</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number"><?php echo $genre['tv_show_count']; ?></div>
                                    <div class="stat-label">TV Shows</div>
                                </div>
                                <div class="stat">
                                    <div class="stat-number"><?php echo $genre['movie_count'] + $genre['tv_show_count']; ?></div>
                                    <div class="stat-label">Total</div>
                                </div>
                            </div>
                            
                            <div class="genre-actions">
                                <a href="movies.php?genre=<?php echo $genre['id']; ?>" class="genre-btn" onclick="event.stopPropagation()">
                                    View Movies
                                </a>
                                <a href="tv-shows.php?genre=<?php echo $genre['id']; ?>" class="genre-btn" onclick="event.stopPropagation()">
                                    View TV Shows
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-genres">
                    <h3>No genres available</h3>
                    <p>Genres will appear here once you start importing content.</p>
                    <?php if (isAdmin()): ?>
                        <a href="admin/" class="btn btn-primary">Import Content</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // Search functionality
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            const cards = document.querySelectorAll('.genre-card');
            
            cards.forEach(card => {
                const genreName = card.querySelector('.genre-name').textContent.toLowerCase();
                if (genreName.includes(query)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
        
        // Add hover effects
        document.querySelectorAll('.genre-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
