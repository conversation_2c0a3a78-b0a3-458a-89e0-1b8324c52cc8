<?php
// Database Configuration - Example File
// Copy this to database.php and update with your settings

class Database {
    private $host = 'localhost';
    private $db_name = 'streamflix';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }
        return $this->conn;
    }
}

// TMDB API Configuration
define('TMDB_API_KEY', 'YOUR_TMDB_API_KEY_HERE');
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE', 'https://image.tmdb.org/t/p/');

// Embed Servers Configuration
$embed_servers = [
    'autoembed' => [
        'name' => 'AutoEmbed',
        'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
        'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
        'priority' => 1
    ],
    'vidjoy' => [
        'name' => 'VidJoy',
        'movie_url' => 'https://vidjoy.pro/embed/movie/{id}',
        'tv_url' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
        'priority' => 2
    ],
    'vidzee' => [
        'name' => 'VidZee',
        'movie_url' => 'https://player.vidzee.wtf/embed/movie/{id}',
        'tv_url' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
        'priority' => 3
    ]
];

// Site Configuration
define('SITE_NAME', 'StreamFlix');
define('SITE_URL', 'http://localhost/streamflix');
define('ADMIN_EMAIL', '<EMAIL>');

// Security
define('JWT_SECRET', 'your_jwt_secret_key_here');
define('ENCRYPTION_KEY', 'your_encryption_key_here');

session_start();
?>
