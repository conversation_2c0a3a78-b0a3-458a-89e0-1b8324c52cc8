# 🎬 StreamFlix APK Installation Guide

## 📱 Mobile App Installation

### Method 1: Direct Installation
1. Download `streamflix-mobile-installer.bat`
2. Run the installer on your computer
3. Follow the instructions to transfer APK to phone
4. Install on Android device

### Method 2: ADB Installation
```bash
adb install streamflix-mobile.apk
```

## 📺 TV App Installation

### Method 1: ADB Sideloading
```bash
# Connect to Android TV
adb connect <TV_IP_ADDRESS>

# Install TV APK
adb install streamflix-tv.apk
```

### Method 2: File Manager
1. Copy APK to USB drive
2. Connect USB to Android TV
3. Use file manager to install APK

## 🔧 Configuration

### API Endpoint Setup
1. Open app settings
2. Navigate to "Server Settings"
3. Enter your API URL:
   - Production: `https://your-domain.com/api`
   - Local: `http://*************:8000/api`

### Permissions Required
- Internet access
- Network state
- Wake lock
- Storage access (for downloads)

## 🎯 Features Available

### Mobile App
- Touch-optimized interface
- Video streaming with quality selection
- Search and filters
- Watchlist management
- Continue watching
- Picture-in-picture mode
- Offline downloads

### TV App
- Leanback interface for TV
- D-pad navigation
- Remote control support
- Voice search integration
- Content recommendations
- 4K video support

## 🐛 Troubleshooting

### Installation Issues
- Enable "Unknown Sources" in Android settings
- Check available storage space
- Verify Android version compatibility (5.0+)

### Connection Issues
- Verify API endpoint URL
- Check network connectivity
- Ensure server is running

### Playback Issues
- Check video server availability
- Try different quality settings
- Verify internet speed

## 📞 Support

- GitHub Issues: Create issue for bugs
- Email: <EMAIL>
- Documentation: README.md files

