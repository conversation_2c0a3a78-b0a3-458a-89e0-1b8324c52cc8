package com.streamflix.mobile.ui.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.streamflix.mobile.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _isLoading = MutableLiveData(true)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isAuthenticated = MutableLiveData<Boolean?>()
    val isAuthenticated: LiveData<Boolean?> = _isAuthenticated

    fun checkAuthStatus() {
        viewModelScope.launch {
            try {
                // Simulate splash screen delay
                delay(2000)
                
                // Check if user is authenticated
                val isAuth = authRepository.isUserLoggedIn()
                _isAuthenticated.value = isAuth
                
            } catch (e: Exception) {
                // On error, assume not authenticated
                _isAuthenticated.value = false
            } finally {
                _isLoading.value = false
            }
        }
    }
}
