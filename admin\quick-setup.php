<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$setup_results = [];
$setup_errors = [];

// Handle quick setup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'quick_setup') {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        // Create tables first
        $transaction_started = false;

        try {
            $conn->beginTransaction();
            $transaction_started = true;

            // Create embed_servers table
            $conn->exec("
                CREATE TABLE IF NOT EXISTS embed_servers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    movie_url TEXT NOT NULL,
                    tv_url TEXT NOT NULL,
                    priority INT DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    last_tested TIMESTAMP NULL,
                    last_response_time INT NULL,
                    last_status_code INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $setup_results[] = "Created embed_servers table";

            // Create site_settings table
            $conn->exec("
                CREATE TABLE IF NOT EXISTS site_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT,
                    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            $setup_results[] = "Created site_settings table";

            // Create user_activity table
            $conn->exec("
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
                    content_type ENUM('movie', 'tv_show') NULL,
                    content_id INT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            $setup_results[] = "Created user_activity table";

            // Create watchlist table
            $conn->exec("
                CREATE TABLE IF NOT EXISTS watchlist (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    content_type ENUM('movie', 'tv_show') NOT NULL,
                    content_id INT NOT NULL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ");
            $setup_results[] = "Created watchlist table";

            $conn->commit();
            $transaction_started = false;

        } catch (Exception $e) {
            if ($transaction_started) {
                $conn->rollBack();
            }
            throw $e;
        }

        // Add missing columns to existing tables
        try {
            $columns_to_add = [
                'movies' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'tv_shows' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'is_featured' => 'BOOLEAN DEFAULT 0',
                    'is_trending' => 'BOOLEAN DEFAULT 0'
                ],
                'users' => [
                    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
                ]
            ];

            foreach ($columns_to_add as $table => $columns) {
                foreach ($columns as $column => $definition) {
                    try {
                        // Check if column exists
                        $stmt = $conn->prepare("SHOW COLUMNS FROM {$table} LIKE ?");
                        $stmt->execute([$column]);

                        if ($stmt->rowCount() == 0) {
                            $conn->exec("ALTER TABLE {$table} ADD COLUMN {$column} {$definition}");
                            $setup_results[] = "Added {$column} column to {$table}";
                        }
                    } catch (Exception $e) {
                        $setup_errors[] = "Error adding {$column} to {$table}: " . $e->getMessage();
                    }
                }
            }
        } catch (Exception $e) {
            $setup_errors[] = "Error adding columns: " . $e->getMessage();
        }

        // Insert default data
        try {
            // Insert default embed servers
            $servers = [
                ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
                ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
                ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
                ['SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4],
                ['EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5],
                ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6]
            ];

            $servers_added = 0;
            foreach ($servers as $server) {
                try {
                    $stmt = $conn->prepare("INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES (?, ?, ?, ?, 1)");
                    $stmt->execute($server);
                    if ($stmt->rowCount() > 0) {
                        $servers_added++;
                    }
                } catch (Exception $e) {
                    $setup_errors[] = "Error inserting server {$server[0]}: " . $e->getMessage();
                }
            }

            if ($servers_added > 0) {
                $setup_results[] = "Inserted {$servers_added} embed servers";
            }

            // Insert default site settings
            $settings = [
                ['site_name', 'StreamFlix', 'string', 'Website name'],
                ['site_description', 'Premium Movie & TV Show Streaming Platform', 'string', 'Website description'],
                ['tmdb_api_key', '', 'string', 'TMDB API Key for content import'],
                ['maintenance_mode', '0', 'boolean', 'Enable maintenance mode'],
                ['registration_enabled', '1', 'boolean', 'Allow new user registrations'],
                ['featured_content_limit', '10', 'number', 'Number of featured content items'],
                ['trending_content_limit', '20', 'number', 'Number of trending content items'],
                ['max_servers_per_content', '6', 'number', 'Maximum embed servers per content'],
                ['cache_duration', '3600', 'number', 'Cache duration in seconds'],
                ['auto_import_trending', '0', 'boolean', 'Auto import trending content daily']
            ];

            $settings_added = 0;
            foreach ($settings as $setting) {
                try {
                    $stmt = $conn->prepare("INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                    $stmt->execute($setting);
                    if ($stmt->rowCount() > 0) {
                        $settings_added++;
                    }
                } catch (Exception $e) {
                    $setup_errors[] = "Error inserting setting {$setting[0]}: " . $e->getMessage();
                }
            }

            if ($settings_added > 0) {
                $setup_results[] = "Inserted {$settings_added} site settings";
            }

        } catch (Exception $e) {
            $setup_errors[] = "Error inserting default data: " . $e->getMessage();
        }
        
        // Create directories
        $directories = [
            __DIR__ . '/../backups',
            __DIR__ . '/../cache',
            __DIR__ . '/../tmp',
            __DIR__ . '/../assets/cache'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                file_put_contents($dir . '/.htaccess', "Deny from all\n");
                $setup_results[] = "Created directory: " . basename($dir);
            }
        }
        
        // Set permissions
        try {
            chmod(__DIR__ . '/../backups', 0755);
            chmod(__DIR__ . '/../cache', 0755);
            chmod(__DIR__ . '/../tmp', 0755);
            $setup_results[] = "Set directory permissions";
        } catch (Exception $e) {
            $setup_errors[] = "Permission error: " . $e->getMessage();
        }
        
        // Test database connection and features
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
            $server_count = $stmt->fetchColumn();
            $setup_results[] = "Database test passed - {$server_count} embed servers configured";
            
            $stmt = $conn->query("SELECT COUNT(*) FROM site_settings");
            $settings_count = $stmt->fetchColumn();
            $setup_results[] = "Settings test passed - {$settings_count} settings configured";
            
        } catch (Exception $e) {
            $setup_errors[] = "Database test failed: " . $e->getMessage();
        }
        
        $success_message = "Quick setup completed! " . count($setup_results) . " operations successful.";

    } catch (Exception $e) {
        $error_message = "Quick setup failed: " . $e->getMessage();
    }
}

// Check current system status
try {
    $db = new Database();
    $conn = $db->connect();
    
    $system_status = [];
    
    // Check tables
    $required_tables = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
    foreach ($required_tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '{$table}'");
        $system_status['tables'][$table] = $stmt->rowCount() > 0;
    }
    
    // Check data
    if ($system_status['tables']['embed_servers']) {
        $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
        $system_status['data']['servers'] = $stmt->fetchColumn();
    }
    
    if ($system_status['tables']['site_settings']) {
        $stmt = $conn->query("SELECT COUNT(*) FROM site_settings");
        $system_status['data']['settings'] = $stmt->fetchColumn();
    }
    
    // Check directories
    $required_dirs = ['backups', 'cache', 'tmp'];
    foreach ($required_dirs as $dir) {
        $path = __DIR__ . '/../' . $dir;
        $system_status['directories'][$dir] = is_dir($path) && is_writable($path);
    }
    
    // Overall status
    $all_tables_exist = !in_array(false, $system_status['tables']);
    $has_data = ($system_status['data']['servers'] ?? 0) > 0 && ($system_status['data']['settings'] ?? 0) > 0;
    $all_dirs_exist = !in_array(false, $system_status['directories']);
    
    $system_status['ready'] = $all_tables_exist && $has_data && $all_dirs_exist;
    
} catch (Exception $e) {
    $system_status = ['error' => $e->getMessage()];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Setup - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 900px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .setup-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: var(--dark-bg);
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid var(--border-color);
        }
        
        .status-card.ready {
            border-left-color: #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            font-size: 0.9rem;
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-missing {
            color: #dc3545;
        }
        
        .setup-btn {
            background: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .setup-btn:hover {
            background: #c41e3a;
            transform: translateY(-2px);
        }
        
        .setup-btn:disabled {
            background: var(--border-color);
            cursor: not-allowed;
            transform: none;
        }
        
        .result-item {
            padding: 10px 15px;
            margin-bottom: 5px;
            border-radius: 4px;
            background: var(--dark-bg);
            border-left: 4px solid #28a745;
        }
        
        .error-item {
            border-left-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        
        .ready-badge {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .warning-badge {
            background: #ffc107;
            color: #000;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: var(--text-secondary);
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>🚀 Quick Setup</h1>
            <p>One-click setup for all StreamFlix features</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="database-updater.php">DB Updater</a>
            <a href="quick-setup.php" class="active">Quick Setup</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($success_message)): ?>
            <div class="success-message"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="error-message"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Setup Results -->
        <?php if (!empty($setup_results)): ?>
            <div class="setup-section">
                <h2>✅ Setup Results</h2>
                <?php foreach ($setup_results as $result): ?>
                    <div class="result-item"><?php echo htmlspecialchars($result); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($setup_errors)): ?>
            <div class="setup-section">
                <h2>⚠️ Setup Errors</h2>
                <?php foreach ($setup_errors as $error): ?>
                    <div class="result-item error-item"><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- System Status -->
        <div class="setup-section">
            <h2>📊 System Status</h2>
            
            <?php if (isset($system_status['ready'])): ?>
                <div style="text-align: center; margin-bottom: 20px;">
                    <?php if ($system_status['ready']): ?>
                        <span class="ready-badge">🎉 System Ready!</span>
                    <?php else: ?>
                        <span class="warning-badge">⚠️ Setup Required</span>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="status-grid">
                <!-- Database Tables -->
                <div class="status-card <?php echo (isset($system_status['tables']) && !in_array(false, $system_status['tables'])) ? 'ready' : 'warning'; ?>">
                    <div class="status-title">Database Tables</div>
                    <?php foreach ($system_status['tables'] ?? [] as $table => $exists): ?>
                        <div class="status-item">
                            <span><?php echo $table; ?></span>
                            <span class="<?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                                <?php echo $exists ? '✓' : '✗'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Data Status -->
                <div class="status-card <?php echo (isset($system_status['data']) && array_sum($system_status['data']) > 0) ? 'ready' : 'warning'; ?>">
                    <div class="status-title">Data Status</div>
                    <?php foreach ($system_status['data'] ?? [] as $type => $count): ?>
                        <div class="status-item">
                            <span><?php echo ucfirst($type); ?></span>
                            <span class="<?php echo $count > 0 ? 'status-ok' : 'status-missing'; ?>">
                                <?php echo $count; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Directories -->
                <div class="status-card <?php echo (isset($system_status['directories']) && !in_array(false, $system_status['directories'])) ? 'ready' : 'warning'; ?>">
                    <div class="status-title">Directories</div>
                    <?php foreach ($system_status['directories'] ?? [] as $dir => $exists): ?>
                        <div class="status-item">
                            <span><?php echo $dir; ?></span>
                            <span class="<?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                                <?php echo $exists ? '✓' : '✗'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Quick Setup Action -->
        <div class="setup-section">
            <h2>🔧 One-Click Setup</h2>
            <p>This will automatically configure your StreamFlix installation with all necessary features:</p>
            
            <ul class="feature-list">
                <li>Create all required database tables</li>
                <li>Add performance indexes</li>
                <li>Insert default embed servers (6 servers including LetsEmbed)</li>
                <li>Configure site settings</li>
                <li>Create backup and cache directories</li>
                <li>Set proper permissions</li>
                <li>Test database connectivity</li>
            </ul>
            
            <form method="POST" onsubmit="return confirm('This will update your database structure. Continue?')">
                <input type="hidden" name="action" value="quick_setup">
                <button type="submit" class="setup-btn" <?php echo ($system_status['ready'] ?? false) ? 'disabled' : ''; ?>>
                    <?php echo ($system_status['ready'] ?? false) ? '✅ Already Configured' : '🚀 Run Quick Setup'; ?>
                </button>
            </form>
            
            <?php if ($system_status['ready'] ?? false): ?>
                <div style="text-align: center; margin-top: 15px;">
                    <p style="color: var(--text-secondary);">Your system is already configured and ready to use!</p>
                    <a href="index.php" class="btn btn-primary">Go to Dashboard</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
