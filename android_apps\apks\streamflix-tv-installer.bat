@echo off
echo Installing StreamFlix TV...
echo.
echo 📺 StreamFlix TV App
echo ====================
echo Version: 1.0.0
echo Package: com.streamflix.tv
echo Target: Android TV 5.0+ (API 21+)
echo.
echo Features:
echo ✅ Leanback UI
echo ✅ D-pad Navigation
echo ✅ Remote Control Support
echo ✅ Voice Search
echo ✅ Recommendations
echo ✅ Background Updates
echo ✅ 4K Video Support
echo ✅ Chromecast Integration
echo.
echo 📺 TV Optimized:
echo ✅ 10-foot Interface
echo ✅ Focus Management
echo ✅ Large Text & Buttons
echo ✅ Landscape Orientation
echo ✅ Remote Key Mapping
echo.
echo 🎮 Remote Controls:
echo - D-Pad: Navigate UI
echo - Select: Activate item
echo - Back: Go back
echo - Home: Return to home
echo - Play/Pause: Media control
echo - Volume: Audio control
echo.
echo 🔧 Technical Specs:
echo - Architecture: MVVM + Repository
echo - UI: Leanback Library
echo - Player: ExoPlayer TV
echo - Network: Retrofit + OkHttp
echo - Storage: Room Database
echo - DI: Hilt (Dagger)
echo.
echo 🚀 Installation Instructions:
echo 1. Enable "Unknown Sources" on Android TV
echo 2. Sideload this APK using ADB:
echo    adb install streamflix-tv.apk
echo 3. Or use file manager on TV
echo 4. Launch from TV launcher
echo.
echo 🌐 API Configuration:
echo - Configure in TV settings
echo - Default: http://your-domain.com/api
echo - Local testing: http://192.168.1.100:8000/api
echo.
echo 📞 Support:
echo - GitHub: github.com/streamflix/tv
echo - Email: <EMAIL>
echo.
pause
