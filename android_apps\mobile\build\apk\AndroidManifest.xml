<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- Hardware features -->
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".StreamFlixApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.StreamFlix.Splash"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.StreamFlix.Splash"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize">
            
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- Deep linking -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="streamflix" />
            </intent-filter>

            <!-- Handle video files -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="video/*" />
            </intent-filter>
        </activity>

        <!-- Player Activity -->
        <activity
            android:name=".ui.player.PlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix.Player"
            android:screenOrientation="sensor"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:supportsPictureInPicture="true" />

        <!-- Auth Activity -->
        <activity
            android:name=".ui.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait" />

        <!-- Details Activity -->
        <activity
            android:name=".ui.details.DetailsActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait" />

        <!-- Search Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlix"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- Download Service -->
        <service
            android:name=".service.DownloadService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Media Session Service -->
        <service
            android:name=".service.MediaSessionService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.media.browse.MediaBrowserService" />
            </intent-filter>
        </service>

        <!-- Notification Receiver -->
        <receiver
            android:name=".receiver.NotificationReceiver"
            android:enabled="true"
            android:exported="false" />

        <!-- File Provider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Work Manager -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

    </application>

    <!-- Queries for Android 11+ -->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="*/*" />
        </intent>
    </queries>

</manifest>
