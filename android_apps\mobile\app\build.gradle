plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
}

android {
    namespace 'com.streamflix.mobile'
    compileSdk 34

    defaultConfig {
        applicationId "com.streamflix.mobile"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }

        // API Configuration
        buildConfigField "String", "API_BASE_URL", '"http://your-domain.com/api"'
        buildConfigField "String", "TMDB_IMAGE_BASE_URL", '"https://image.tmdb.org/t/p/"'
        buildConfigField "boolean", "DEBUG_MODE", "true"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"http://10.0.2.2:8000/api"'
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", '"https://your-domain.com/api"'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.5.8'
    }

    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation platform('androidx.compose:compose-bom:2024.02.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'

    // Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-compose:2.7.6'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // Dependency Injection
    implementation 'com.google.dagger:hilt-android:2.48.1'
    kapt 'com.google.dagger:hilt-compiler:2.48.1'
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'

    // Network
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Image Loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'io.coil-kt:coil-compose:2.5.0'

    // Video Player
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-cast:2.19.1'

    // Local Storage
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'

    // Preferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'

    // Material Design
    implementation 'com.google.android.material:material:1.11.0'

    // Swipe Refresh
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // ViewPager2
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // Fragment
    implementation 'androidx.fragment:fragment-ktx:1.6.2'

    // Work Manager
    implementation 'androidx.work:work-runtime-ktx:2.9.0'

    // Biometric
    implementation 'androidx.biometric:biometric:1.1.0'

    // Splash Screen
    implementation 'androidx.core:core-splashscreen:1.0.1'

    // Lottie Animations
    implementation 'com.airbnb.android:lottie:6.2.0'

    // Shimmer Effect
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Pull to Refresh
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.02.00')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
