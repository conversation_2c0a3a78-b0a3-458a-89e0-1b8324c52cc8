<?php
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

header('Content-Type: application/json');

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';
$type = $input['type'] ?? 'movie';

$streamflix = new StreamFlix();
$imported_count = 0;

try {
    switch ($action) {
        case 'import_trending':
        case 'import_trending_movies':
        case 'import_trending_tv':
            $endpoint = $type === 'movie' ? '/trending/movie/week' : '/trending/tv/week';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    if ($type === 'movie') {
                        $result = $streamflix->importMovie($item['id']);
                    } else {
                        $result = $streamflix->importTVShow($item['id']);
                    }

                    if ($result) {
                        $imported_count++;
                        // Mark as trending
                        $streamflix->markAsTrending($result, $type);
                    }

                    // Small delay to avoid rate limiting
                    usleep(100000); // 0.1 second
                }
            }
            break;

        case 'import_popular':
        case 'import_popular_movies':
        case 'import_popular_tv':
            $endpoint = $type === 'movie' ? '/movie/popular' : '/tv/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    if ($type === 'movie') {
                        $result = $streamflix->importMovie($item['id']);
                    } else {
                        $result = $streamflix->importTVShow($item['id']);
                    }

                    if ($result) {
                        $imported_count++;
                        // Mark as featured
                        $streamflix->markAsFeatured($result, $type);
                    }

                    // Small delay to avoid rate limiting
                    usleep(100000); // 0.1 second
                }
            }
            break;

        case 'import_now_playing':
            $endpoint = '/movie/now_playing';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importMovie($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsFeatured($result, 'movie');
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_upcoming':
            $endpoint = '/movie/upcoming';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importMovie($item['id']);
                    if ($result) {
                        $imported_count++;
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_airing_today':
            $endpoint = '/tv/airing_today';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importTVShow($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsTrending($result, 'tv_show');
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_on_the_air':
            $endpoint = '/tv/on_the_air';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importTVShow($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsFeatured($result, 'tv_show');
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_anime_movies':
            // Import popular movies and mark as anime
            $endpoint = '/discover/movie';
            $params = '?with_genres=16&sort_by=popularity.desc'; // 16 = Animation
            $data = $streamflix->fetchFromTMDB($endpoint . $params);

            if ($data && isset($data['results'])) {
                foreach (array_slice($data['results'], 0, 20) as $item) {
                    try {
                        $result = $streamflix->importMovie($item['id']);
                        if ($result) {
                            $imported_count++;
                            markAsAnime($result, 'movie');
                        }
                        usleep(300000); // 0.3 seconds delay
                    } catch (Exception $e) {
                        continue;
                    }
                }
            }
            break;

        case 'import_anime_tv':
            // Import popular TV shows and mark as anime
            $endpoint = '/discover/tv';
            $params = '?with_genres=16&sort_by=popularity.desc'; // 16 = Animation
            $data = $streamflix->fetchFromTMDB($endpoint . $params);

            if ($data && isset($data['results'])) {
                foreach (array_slice($data['results'], 0, 20) as $item) {
                    try {
                        $result = $streamflix->importTVShow($item['id']);
                        if ($result) {
                            $imported_count++;
                            markAsAnime($result, 'tv_show');
                        }
                        usleep(300000); // 0.3 seconds delay
                    } catch (Exception $e) {
                        continue;
                    }
                }
            }
            break;

        case 'import_hentai':
            // Import animation TV shows and mark as hentai
            $endpoint = '/discover/tv';
            $params = '?with_genres=16&sort_by=popularity.desc'; // Animation genre
            $data = $streamflix->fetchFromTMDB($endpoint . $params);

            if ($data && isset($data['results'])) {
                foreach (array_slice($data['results'], 0, 10) as $item) { // Smaller limit for hentai
                    try {
                        $result = $streamflix->importTVShow($item['id']);
                        if ($result) {
                            $imported_count++;
                            markAsHentai($result);
                        }
                        usleep(300000); // 0.3 seconds delay
                    } catch (Exception $e) {
                        continue;
                    }
                }
            }
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
    echo json_encode([
        'success' => true,
        'count' => $imported_count,
        'message' => "Successfully imported {$imported_count} items"
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function markAsAnime($content_id, $content_type) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Create anime genre if it doesn't exist
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Anime', 16]); // 16 is TMDB's Animation genre ID

        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Anime'");
        $stmt->execute();
        $genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($genre) {
            if ($content_type === 'movie') {
                // Add to movie_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            } else {
                // Add to tv_show_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            }
        }
    } catch (Exception $e) {
        // Ignore errors
        error_log("Error marking as anime: " . $e->getMessage());
    }
}

function markAsHentai($content_id) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Create hentai genre if it doesn't exist
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Hentai', 99999]); // Using a high number for custom genre

        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Hentai'");
        $stmt->execute();
        $genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($genre) {
            // Add to tv_show_genres (hentai is typically TV show format)
            $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
            $stmt->execute([$content_id, $genre['id']]);
        }
    } catch (Exception $e) {
        // Ignore errors
        error_log("Error marking as hentai: " . $e->getMessage());
    }
}
?>
