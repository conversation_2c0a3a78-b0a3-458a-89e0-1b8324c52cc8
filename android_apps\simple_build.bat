@echo off
echo 🎬 StreamFlix Simple APK Builder
echo =================================

:: Set colors
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%📱 Creating simplified APK structure...%NC%

:: Create output directory
if not exist "outputs" mkdir outputs

:: Create mobile APK structure
echo %BLUE%📱 Building Mobile APK structure...%NC%
if not exist "outputs\mobile" mkdir outputs\mobile
if not exist "outputs\mobile\META-INF" mkdir outputs\mobile\META-INF
if not exist "outputs\mobile\res" mkdir outputs\mobile\res
if not exist "outputs\mobile\assets" mkdir outputs\mobile\assets

:: Create TV APK structure  
echo %BLUE%📺 Building TV APK structure...%NC%
if not exist "outputs\tv" mkdir outputs\tv
if not exist "outputs\tv\META-INF" mkdir outputs\tv\META-INF
if not exist "outputs\tv\res" mkdir outputs\tv\res
if not exist "outputs\tv\assets" mkdir outputs\tv\assets

:: Create Android Manifest for Mobile
echo %BLUE%📝 Creating Mobile AndroidManifest.xml...%NC%
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<manifest xmlns:android="http://schemas.android.com/apk/res/android"
echo     package="com.streamflix.mobile"
echo     android:versionCode="1"
echo     android:versionName="1.0.0"^>
echo.
echo     ^<uses-permission android:name="android.permission.INTERNET" /^>
echo     ^<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /^>
echo     ^<uses-permission android:name="android.permission.WAKE_LOCK" /^>
echo.
echo     ^<application
echo         android:allowBackup="true"
echo         android:icon="@mipmap/ic_launcher"
echo         android:label="StreamFlix"
echo         android:theme="@style/AppTheme"^>
echo.
echo         ^<activity
echo             android:name=".MainActivity"
echo             android:exported="true"^>
echo             ^<intent-filter^>
echo                 ^<action android:name="android.intent.action.MAIN" /^>
echo                 ^<category android:name="android.intent.category.LAUNCHER" /^>
echo             ^</intent-filter^>
echo         ^</activity^>
echo.
echo     ^</application^>
echo ^</manifest^>
) > outputs\mobile\AndroidManifest.xml

:: Create Android Manifest for TV
echo %BLUE%📝 Creating TV AndroidManifest.xml...%NC%
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<manifest xmlns:android="http://schemas.android.com/apk/res/android"
echo     package="com.streamflix.tv"
echo     android:versionCode="1"
echo     android:versionName="1.0.0"^>
echo.
echo     ^<uses-permission android:name="android.permission.INTERNET" /^>
echo     ^<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /^>
echo     ^<uses-permission android:name="android.permission.WAKE_LOCK" /^>
echo.
echo     ^<uses-feature
echo         android:name="android.software.leanback"
echo         android:required="true" /^>
echo     ^<uses-feature
echo         android:name="android.hardware.touchscreen"
echo         android:required="false" /^>
echo.
echo     ^<application
echo         android:allowBackup="true"
echo         android:icon="@mipmap/ic_launcher"
echo         android:banner="@drawable/app_banner"
echo         android:label="StreamFlix TV"
echo         android:theme="@style/Theme.Leanback"^>
echo.
echo         ^<activity
echo             android:name=".MainActivity"
echo             android:exported="true"
echo             android:screenOrientation="landscape"^>
echo             ^<intent-filter^>
echo                 ^<action android:name="android.intent.action.MAIN" /^>
echo                 ^<category android:name="android.intent.category.LEANBACK_LAUNCHER" /^>
echo             ^</intent-filter^>
echo         ^</activity^>
echo.
echo     ^</application^>
echo ^</manifest^>
) > outputs\tv\AndroidManifest.xml

:: Create basic resources
echo %BLUE%🎨 Creating basic resources...%NC%

:: Create strings.xml for mobile
if not exist "outputs\mobile\res\values" mkdir outputs\mobile\res\values
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<resources^>
echo     ^<string name="app_name"^>StreamFlix^</string^>
echo ^</resources^>
) > outputs\mobile\res\values\strings.xml

:: Create strings.xml for TV
if not exist "outputs\tv\res\values" mkdir outputs\tv\res\values
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<resources^>
echo     ^<string name="app_name"^>StreamFlix TV^</string^>
echo ^</resources^>
) > outputs\tv\res\values\strings.xml

:: Create demo APK files (placeholder)
echo %BLUE%📦 Creating demo APK files...%NC%

:: Mobile APK
echo Creating StreamFlix Mobile APK... > outputs\streamflix-mobile-demo.apk
echo Package: com.streamflix.mobile >> outputs\streamflix-mobile-demo.apk
echo Version: 1.0.0 >> outputs\streamflix-mobile-demo.apk
echo Features: Video streaming, Search, Watchlist >> outputs\streamflix-mobile-demo.apk
echo Target: Android phones and tablets >> outputs\streamflix-mobile-demo.apk

:: TV APK
echo Creating StreamFlix TV APK... > outputs\streamflix-tv-demo.apk
echo Package: com.streamflix.tv >> outputs\streamflix-tv-demo.apk
echo Version: 1.0.0 >> outputs\streamflix-tv-demo.apk
echo Features: Leanback UI, D-pad navigation, Remote control >> outputs\streamflix-tv-demo.apk
echo Target: Android TV and Google TV >> outputs\streamflix-tv-demo.apk

echo.
echo %GREEN%✅ Demo APK structure created successfully!%NC%
echo.
echo %BLUE%📁 Output files:%NC%
echo   outputs\streamflix-mobile-demo.apk
echo   outputs\streamflix-tv-demo.apk
echo   outputs\mobile\AndroidManifest.xml
echo   outputs\tv\AndroidManifest.xml
echo.
echo %YELLOW%⚠️  Note: These are demo APK structures.%NC%
echo %YELLOW%   For actual APK building, you need:%NC%
echo   - Android Studio installed
echo   - Android SDK configured
echo   - Gradle build system
echo.
echo %BLUE%🚀 To build real APKs:%NC%
echo   1. Install Android Studio
echo   2. Open projects in Android Studio
echo   3. Build ^> Generate Signed Bundle/APK
echo.
echo %GREEN%🎉 Demo build completed!%NC%
pause
