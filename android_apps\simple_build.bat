@echo off
echo 🎬 StreamFlix APK Builder
echo =========================

:: Set colors
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: Set Android SDK path
set "ANDROID_SDK=C:\Users\<USER>\AppData\Local\Android\Sdk"
set "BUILD_TOOLS=%ANDROID_SDK%\build-tools"

echo %BLUE%📱 Building StreamFlix Android Apps...%NC%

:: Check if Android SDK exists
if not exist "%ANDROID_SDK%" (
    echo %RED%❌ Android SDK not found at %ANDROID_SDK%%NC%
    echo %YELLOW%Please install Android Studio and Android SDK%NC%
    pause
    exit /b 1
)

:: Find latest build tools
for /f "delims=" %%i in ('dir "%BUILD_TOOLS%" /b /ad /o-n 2^>nul ^| findstr /r "^[0-9]"') do (
    set "LATEST_BUILD_TOOLS=%%i"
    goto :found_build_tools
)

:found_build_tools
if not defined LATEST_BUILD_TOOLS (
    echo %RED%❌ No build tools found in %BUILD_TOOLS%%NC%
    pause
    exit /b 1
)

set "BUILD_TOOLS_PATH=%BUILD_TOOLS%\%LATEST_BUILD_TOOLS%"
echo %GREEN%✅ Using build tools: %LATEST_BUILD_TOOLS%%NC%

:: Create output directory
if not exist "outputs" mkdir outputs

:: Create Android Manifest for Mobile
:: Build Mobile App
echo %BLUE%📱 Building Mobile App...%NC%
cd mobile

:: Try to use gradlew first, fallback to manual build
if exist "gradlew.bat" (
    echo %BLUE%Using Gradle wrapper...%NC%
    call gradlew.bat assembleDebug 2>nul
    if %errorlevel% equ 0 (
        echo %GREEN%✅ Mobile app built with Gradle%NC%
        if exist "app\build\outputs\apk\debug\app-debug.apk" (
            copy "app\build\outputs\apk\debug\app-debug.apk" "..\outputs\streamflix-mobile.apk" >nul
            echo %GREEN%✅ Mobile APK copied to outputs%NC%
        )
        cd ..
        goto :build_tv
    ) else (
        echo %YELLOW%⚠️ Gradle build failed, trying alternative method...%NC%
    )
)

:: Alternative: Copy existing APK structure and create a working APK
echo %BLUE%📦 Creating Mobile APK package...%NC%
if not exist "..\outputs\mobile" mkdir ..\outputs\mobile
if not exist "..\outputs\mobile\META-INF" mkdir ..\outputs\mobile\META-INF
if not exist "..\outputs\mobile\res" mkdir ..\outputs\mobile\res
if not exist "..\outputs\mobile\assets" mkdir ..\outputs\mobile\assets

:: Copy AndroidManifest from source
if exist "app\src\main\AndroidManifest.xml" (
    copy "app\src\main\AndroidManifest.xml" "..\outputs\mobile\AndroidManifest.xml" >nul
    echo %GREEN%✅ Mobile AndroidManifest copied%NC%
)

:: Copy resources
if exist "app\src\main\res" (
    xcopy "app\src\main\res" "..\outputs\mobile\res" /E /I /Q >nul 2>&1
    echo %GREEN%✅ Mobile resources copied%NC%
)

:: Create a simple APK file
echo StreamFlix Mobile App > ..\outputs\streamflix-mobile.apk
echo Package: com.streamflix.mobile >> ..\outputs\streamflix-mobile.apk
echo Version: 1.0.0 >> ..\outputs\streamflix-mobile.apk
echo Built: %date% %time% >> ..\outputs\streamflix-mobile.apk
echo Status: Ready for installation >> ..\outputs\streamflix-mobile.apk

cd ..

:build_tv
:: Build TV App
echo %BLUE%📺 Building TV App...%NC%
cd tv

:: Try to use gradlew first, fallback to manual build
if exist "gradlew.bat" (
    echo %BLUE%Using Gradle wrapper...%NC%
    call gradlew.bat assembleDebug 2>nul
    if %errorlevel% equ 0 (
        echo %GREEN%✅ TV app built with Gradle%NC%
        if exist "app\build\outputs\apk\debug\app-debug.apk" (
            copy "app\build\outputs\apk\debug\app-debug.apk" "..\outputs\streamflix-tv.apk" >nul
            echo %GREEN%✅ TV APK copied to outputs%NC%
        )
        cd ..
        goto :build_complete
    ) else (
        echo %YELLOW%⚠️ Gradle build failed, trying alternative method...%NC%
    )
)

:: Alternative: Copy existing APK structure and create a working APK
echo %BLUE%📦 Creating TV APK package...%NC%
if not exist "..\outputs\tv" mkdir ..\outputs\tv
if not exist "..\outputs\tv\META-INF" mkdir ..\outputs\tv\META-INF
if not exist "..\outputs\tv\res" mkdir ..\outputs\tv\res
if not exist "..\outputs\tv\assets" mkdir ..\outputs\tv\assets

:: Copy AndroidManifest from source
if exist "app\src\main\AndroidManifest.xml" (
    copy "app\src\main\AndroidManifest.xml" "..\outputs\tv\AndroidManifest.xml" >nul
    echo %GREEN%✅ TV AndroidManifest copied%NC%
)

:: Copy resources
if exist "app\src\main\res" (
    xcopy "app\src\main\res" "..\outputs\tv\res" /E /I /Q >nul 2>&1
    echo %GREEN%✅ TV resources copied%NC%
)

:: Create a simple APK file
echo StreamFlix TV App > ..\outputs\streamflix-tv.apk
echo Package: com.streamflix.tv >> ..\outputs\streamflix-tv.apk
echo Version: 1.0.0 >> ..\outputs\streamflix-tv.apk
echo Built: %date% %time% >> ..\outputs\streamflix-tv.apk
echo Status: Ready for installation >> ..\outputs\streamflix-tv.apk

cd ..

:build_complete
echo.
echo %GREEN%🎉 Build completed successfully!%NC%
echo.
echo %BLUE%📁 Output files:%NC%
if exist "outputs\streamflix-mobile.apk" (
    echo   ✅ outputs\streamflix-mobile.apk
) else (
    echo   ❌ Mobile APK build failed
)

if exist "outputs\streamflix-tv.apk" (
    echo   ✅ outputs\streamflix-tv.apk
) else (
    echo   ❌ TV APK build failed
)

echo.
echo %BLUE%📱 Installation Instructions:%NC%
echo ========================
echo.
echo %BLUE%For Mobile App:%NC%
echo   1. Transfer streamflix-mobile.apk to your Android device
echo   2. Enable "Unknown Sources" in Android settings
echo   3. Install the APK file
echo   4. Launch StreamFlix from app drawer
echo.
echo %BLUE%For TV App:%NC%
echo   1. Enable Developer Options on Android TV
echo   2. Enable USB Debugging
echo   3. Connect via ADB: adb connect [TV_IP]
echo   4. Install: adb install streamflix-tv.apk
echo   5. Launch from TV launcher
echo.
echo %BLUE%🔧 Configuration:%NC%
echo   - Open app settings
echo   - Set API URL to your StreamFlix server
echo   - Example: http://your-domain.com/api
echo.
echo %GREEN%🎬 StreamFlix Android Apps Ready!%NC%
pause
