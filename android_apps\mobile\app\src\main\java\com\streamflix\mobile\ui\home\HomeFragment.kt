package com.streamflix.mobile.ui.home

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.streamflix.mobile.databinding.FragmentHomeBinding
import com.streamflix.mobile.ui.adapter.ContentAdapter
import com.streamflix.mobile.ui.adapter.HeroAdapter
import com.streamflix.mobile.ui.details.DetailsActivity
import com.streamflix.mobile.ui.player.PlayerActivity
import com.streamflix.mobile.utils.gone
import com.streamflix.mobile.utils.visible
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private val viewModel: HomeViewModel by viewModels()

    private lateinit var heroAdapter: HeroAdapter
    private lateinit var featuredMoviesAdapter: ContentAdapter
    private lateinit var trendingMoviesAdapter: ContentAdapter
    private lateinit var featuredTvAdapter: ContentAdapter
    private lateinit var continueWatchingAdapter: ContentAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerViews()
        setupObservers()
        setupSwipeRefresh()
        
        // Load data
        viewModel.loadHomeData()
    }

    private fun setupRecyclerViews() {
        // Hero ViewPager
        heroAdapter = HeroAdapter { content ->
            navigateToPlayer(content)
        }
        binding.viewPagerHero.adapter = heroAdapter

        // Featured Movies
        featuredMoviesAdapter = ContentAdapter(
            onItemClick = { content -> navigateToDetails(content) },
            onPlayClick = { content -> navigateToPlayer(content) }
        )
        binding.recyclerFeaturedMovies.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = featuredMoviesAdapter
        }

        // Trending Movies
        trendingMoviesAdapter = ContentAdapter(
            onItemClick = { content -> navigateToDetails(content) },
            onPlayClick = { content -> navigateToPlayer(content) }
        )
        binding.recyclerTrendingMovies.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = trendingMoviesAdapter
        }

        // Featured TV Shows
        featuredTvAdapter = ContentAdapter(
            onItemClick = { content -> navigateToDetails(content) },
            onPlayClick = { content -> navigateToPlayer(content) }
        )
        binding.recyclerFeaturedTv.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = featuredTvAdapter
        }

        // Continue Watching
        continueWatchingAdapter = ContentAdapter(
            onItemClick = { content -> navigateToPlayer(content) },
            onPlayClick = { content -> navigateToPlayer(content) },
            showProgress = true
        )
        binding.recyclerContinueWatching.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = continueWatchingAdapter
        }
    }

    private fun setupObservers() {
        viewModel.heroContent.observe(viewLifecycleOwner) { content ->
            if (content.isNotEmpty()) {
                heroAdapter.submitList(content)
                binding.viewPagerHero.visible()
            } else {
                binding.viewPagerHero.gone()
            }
        }

        viewModel.featuredMovies.observe(viewLifecycleOwner) { movies ->
            if (movies.isNotEmpty()) {
                featuredMoviesAdapter.submitList(movies)
                binding.sectionFeaturedMovies.visible()
            } else {
                binding.sectionFeaturedMovies.gone()
            }
        }

        viewModel.trendingMovies.observe(viewLifecycleOwner) { movies ->
            if (movies.isNotEmpty()) {
                trendingMoviesAdapter.submitList(movies)
                binding.sectionTrendingMovies.visible()
            } else {
                binding.sectionTrendingMovies.gone()
            }
        }

        viewModel.featuredTvShows.observe(viewLifecycleOwner) { tvShows ->
            if (tvShows.isNotEmpty()) {
                featuredTvAdapter.submitList(tvShows)
                binding.sectionFeaturedTv.visible()
            } else {
                binding.sectionFeaturedTv.gone()
            }
        }

        viewModel.continueWatching.observe(viewLifecycleOwner) { content ->
            if (content.isNotEmpty()) {
                continueWatchingAdapter.submitList(content)
                binding.sectionContinueWatching.visible()
            } else {
                binding.sectionContinueWatching.gone()
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefresh.isRefreshing = isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            if (error != null) {
                // Show error message
                // You can implement a Snackbar or Toast here
            }
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            viewModel.refreshHomeData()
        }
    }

    private fun navigateToDetails(content: Any) {
        val intent = Intent(requireContext(), DetailsActivity::class.java).apply {
            // Pass content data
            putExtra("content", content as? android.os.Parcelable)
        }
        startActivity(intent)
    }

    private fun navigateToPlayer(content: Any) {
        val intent = Intent(requireContext(), PlayerActivity::class.java).apply {
            // Pass content data
            putExtra("content", content as? android.os.Parcelable)
        }
        startActivity(intent)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
