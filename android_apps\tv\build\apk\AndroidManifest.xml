<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- TV Features -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.gamepad"
        android:required="false" />

    <application
        android:name=".StreamFlixTvApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:banner="@drawable/app_banner"
        android:label="@string/app_name"
        android:theme="@style/Theme.StreamFlixTV"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- Main TV Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.StreamFlixTV"
            android:screenOrientation="landscape">
            
            <!-- TV Launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>

            <!-- Standard Launcher (fallback) -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Player Activity -->
        <activity
            android:name=".ui.player.PlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlixTV.Player"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <!-- Details Activity -->
        <activity
            android:name=".ui.details.DetailsActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlixTV"
            android:screenOrientation="landscape" />

        <!-- Search Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlixTV.Search"
            android:screenOrientation="landscape" />

        <!-- Settings Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.StreamFlixTV"
            android:screenOrientation="landscape" />

        <!-- Recommendation Service -->
        <service
            android:name=".service.RecommendationService"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>
